import request from '@/utils/request'

// 根据pid获取数据列表
export function getDataList(pid) {
  return request({
    url: `/attribute-dict/list/${pid}`,
    method: 'get'
  })
}

// 获取数据树形列表
export function getDataTreeList() {
  return request({
    url: '/attribute-dict',
    method: 'get'
  })
}

// 新增数据
export function addData(data) {
  return request({
    url: '/attribute-dict',
    method: 'post',
    data
  })
}

// 修改数据
export function updateData(data) {
  return request({
    url: '/attribute-dict',
    method: 'put',
    data
  })
}

// 删除数据
export function deleteData(id) {
  return request({
    url: `/attribute-dict/${id}`,
    method: 'delete'
  })
}

// 获取数据详情
export function getDataDetail(id) {
  return request({
    url: `/attribute-dict/${id}`,
    method: 'get'
  })
}

// 获取表格数据
export function getTableList(query) {
  return request({
    url: '/feature',
    method: 'get',
    params: query
  })
}

// 导入数据
export function uploadData(data) {
  return request({
    url: '/resource-file/upload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 新增导入数据
export function addImportData(data) {
  return request({
    url: '/feature',
    method: 'post',
    data
  })
}

// 删除表格数据
export function deleteTableData(id) {
  return request({
    url: `/feature/${id}`,
    method: 'delete'
  })
}

// 更新表格数据
export function updateTableData(data) {
  return request({
    url: '/feature',
    method: 'put',
    data
  })
}

// 获取详情
export function getTableDetail(id) {
  return request({
    url: `/feature/${id}`,
    method: 'get'
  })
}
