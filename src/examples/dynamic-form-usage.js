// 动态表单使用示例

import { getUserFormConfig, getProductFormConfig, defaultOptions } from '@/config/form-configs.js'

// 示例1: 基础用法
export const basicUsage = () => {
  // 使用默认选项
  const userConfig = getUserFormConfig(defaultOptions)
  
  return {
    config: userConfig,
    data: {
      username: '',
      email: '',
      age: null,
      gender: '',
      status: 'active',
      interests: [],
      department: '',
      region: [],
      birthday: '',
      isVip: false
    }
  }
}

// 示例2: 动态选项用法
export const dynamicOptionsUsage = () => {
  // 模拟从API获取的动态数据
  const dynamicOptions = {
    // 部门选项 - 从API获取
    departmentOptions: [
      { label: '技术部', value: 'tech' },
      { label: '产品部', value: 'product' },
      { label: '运营部', value: 'operation' },
      { label: '市场部', value: 'marketing' }
    ],
    
    // 地区选项 - 从API获取
    regionOptions: [
      {
        value: 'china',
        label: '中国',
        children: [
          {
            value: 'beijing',
            label: '北京市',
            children: [
              { value: 'chaoyang', label: '朝阳区' },
              { value: 'haidian', label: '海淀区' }
            ]
          },
          {
            value: 'shanghai',
            label: '上海市',
            children: [
              { value: 'huangpu', label: '黄浦区' },
              { value: 'xuhui', label: '徐汇区' }
            ]
          }
        ]
      }
    ]
  }
  
  // 合并默认选项和动态选项
  const options = {
    ...defaultOptions,
    ...dynamicOptions
  }
  
  const userConfig = getUserFormConfig(options)
  
  return {
    config: userConfig,
    data: {},
    options: dynamicOptions
  }
}

// 示例3: 产品表单用法
export const productFormUsage = () => {
  const dynamicOptions = {
    // 品牌选项
    brandOptions: [
      { label: '苹果', value: 'apple' },
      { label: '华为', value: 'huawei' },
      { label: '小米', value: 'xiaomi' }
    ],
    
    // 标签选项
    tagOptions: [
      { label: '热销', value: 'hot' },
      { label: '新品', value: 'new' },
      { label: '促销', value: 'sale' }
    ]
  }
  
  const options = {
    ...defaultOptions,
    ...dynamicOptions
  }
  
  const productConfig = getProductFormConfig(options)
  
  return {
    config: productConfig,
    data: {
      name: '',
      description: '',
      price: null,
      category: '',
      brand: '',
      tags: [],
      salesRegion: [],
      status: 'draft',
      quality: 80,
      rating: 0,
      themeColor: '',
      image: ''
    },
    options: dynamicOptions
  }
}

// 示例4: 自定义配置
export const customConfigUsage = () => {
  // 自定义表单配置
  const customConfig = {
    labelWidth: '120px',
    inline: false,
    items: [
      {
        type: 'input',
        prop: 'title',
        label: '标题',
        placeholder: '请输入标题',
        required: true,
        rules: [{ required: true, message: '请输入标题', trigger: 'blur' }]
      },
      {
        type: 'select',
        prop: 'category',
        label: '分类',
        placeholder: '请选择分类',
        options: [
          { label: '技术文章', value: 'tech' },
          { label: '产品介绍', value: 'product' },
          { label: '公司新闻', value: 'news' }
        ]
      },
      {
        type: 'textarea',
        prop: 'content',
        label: '内容',
        placeholder: '请输入内容',
        rows: 5,
        maxlength: 1000,
        showWordLimit: true
      },
      {
        type: 'switch',
        prop: 'published',
        label: '发布',
        activeText: '已发布',
        inactiveText: '草稿'
      }
    ]
  }
  
  return {
    config: customConfig,
    data: {
      title: '',
      category: '',
      content: '',
      published: false
    }
  }
}

// 示例5: 表单验证
export const validationUsage = () => {
  const config = {
    labelWidth: '100px',
    items: [
      {
        type: 'input',
        prop: 'username',
        label: '用户名',
        placeholder: '请输入用户名',
        required: true,
        rules: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' },
          { pattern: /^[a-zA-Z0-9_]+$/, message: '只能包含字母、数字和下划线', trigger: 'blur' }
        ]
      },
      {
        type: 'input',
        prop: 'email',
        label: '邮箱',
        placeholder: '请输入邮箱',
        inputType: 'email',
        required: true,
        rules: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      },
      {
        type: 'input',
        prop: 'password',
        label: '密码',
        placeholder: '请输入密码',
        inputType: 'password',
        showPassword: true,
        required: true,
        rules: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
        ]
      },
      {
        type: 'input',
        prop: 'confirmPassword',
        label: '确认密码',
        placeholder: '请再次输入密码',
        inputType: 'password',
        showPassword: true,
        required: true,
        rules: [
          { required: true, message: '请再次输入密码', trigger: 'blur' },
          {
            validator: (rule, value, callback, source) => {
              if (value !== source.password) {
                callback(new Error('两次输入密码不一致'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      }
    ]
  }
  
  return {
    config,
    data: {
      username: '',
      email: '',
      password: '',
      confirmPassword: ''
    }
  }
}

// 示例6: 事件处理
export const eventHandlingUsage = () => {
  const config = {
    labelWidth: '100px',
    items: [
      {
        type: 'select',
        prop: 'province',
        label: '省份',
        placeholder: '请选择省份',
        options: [
          { label: '北京', value: 'beijing' },
          { label: '上海', value: 'shanghai' },
          { label: '广东', value: 'guangdong' }
        ],
        onChange: (value, formData) => {
          console.log('省份变化:', value)
          // 清空城市选择
          formData.city = ''
          // 根据省份加载城市数据
          loadCities(value)
        }
      },
      {
        type: 'select',
        prop: 'city',
        label: '城市',
        placeholder: '请选择城市',
        options: [] // 动态加载
      }
    ]
  }
  
  const loadCities = (province) => {
    // 模拟根据省份加载城市
    const cityMap = {
      beijing: [
        { label: '朝阳区', value: 'chaoyang' },
        { label: '海淀区', value: 'haidian' }
      ],
      shanghai: [
        { label: '黄浦区', value: 'huangpu' },
        { label: '徐汇区', value: 'xuhui' }
      ],
      guangdong: [
        { label: '广州市', value: 'guangzhou' },
        { label: '深圳市', value: 'shenzhen' }
      ]
    }
    
    // 更新城市选项
    const cityItem = config.items.find(item => item.prop === 'city')
    if (cityItem) {
      cityItem.options = cityMap[province] || []
    }
  }
  
  return {
    config,
    data: {
      province: '',
      city: ''
    },
    methods: {
      loadCities
    }
  }
}
