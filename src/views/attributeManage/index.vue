<template>
  <div class="attribute-manage app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Sort" @click="toggleExpandAll">展开/折叠</el-button>
      </el-col>
    </el-row>

    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="tableData"
      row-key="attributeId"
      :default-expand-all="isExpandAll"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column
        prop="attributeName"
        label="名称"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column
        prop="attributeLabel"
        label="key"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column prop="sort" label="序号" :show-overflow-tooltip="true"></el-table-column>
      <!-- <el-table-column
        prop="attributeValue"
        label="value"
        :show-overflow-tooltip="true"
      ></el-table-column> -->
      <el-table-column prop="level" label="级别" :show-overflow-tooltip="true">
        <template #default="scope">
          <el-tag :type="findLevel(scope.row.level)?.type" size="small">
            {{ findLevel(scope.row.level)?.label }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" align="center" width="160" prop="createTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="210"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">
            修改
          </el-button>
          <el-button link type="primary" icon="Plus" @click="handleAdd(scope.row)">新增</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <Dialog ref="dialogRef" @submit="getTableDataList"></Dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Dialog from './dialog.vue'
import { deleteData, getDataDetail, getDataTreeList } from '../../api/dataStore'
import { getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'

const { proxy } = getCurrentInstance()

const refreshTable = ref(true)
const loading = ref(false)
const isExpandAll = ref(false)
const tableData = ref([])
const dialogRef = ref(null)

const levelOptions = [
  { label: '一级', value: 1, type: 'primary' },
  { label: '二级', value: 2, type: 'success' },
  { label: '三级', value: 3, type: 'warning' }
]

// 查找等级
function findLevel(value) {
  return levelOptions.find((item) => item.value == value)
}

async function getTableDataList() {
  try {
    loading.value = true
    const res = await getDataTreeList()
    tableData.value = proxy.handleTree(res.data, 'attributeId', 'attributePid')
    loading.value = false
  } catch (error) {
    loading.value = false
    ElMessage.error('获取数据失败，请稍后重试')
  }
}
getTableDataList()

function handleAdd(row) {
  dialogRef.value.setVisible('新增菜单', {
    attributePid: row && row.attributeId ? row.attributeId : '0'
  })
}

/** 展开/折叠操作 */
function toggleExpandAll() {
  refreshTable.value = false
  isExpandAll.value = !isExpandAll.value
  nextTick(() => {
    refreshTable.value = true
  })
}

async function handleUpdate(row) {
  const res = await getDataDetail(row.attributeId)
  dialogRef.value.setVisible('修改菜单', res.data)
}

async function handleDelete(row) {
  const res = await deleteData(row.attributeId)
  if (res.code == 200) {
    ElMessage.success('删除成功')
    getTableDataList()
  }
}
</script>

<style scoped lang="scss">
.attribute-manage {
}
</style>
