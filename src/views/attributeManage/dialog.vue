<template>
  <div class="dialog">
    <el-dialog :title="title" v-model="dialogVisible">
      <el-form :model="attributeDate" label-width="80px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="名称">
              <el-input v-model="attributeDate.attributeName" placeholder="请输入名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="key">
              <el-input v-model="attributeDate.attributeLabel" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上级类型">
              <el-tree-select
                v-model="attributeDate.attributePid"
                :data="treeOptions"
                :props="{ children: 'children', label: 'attributeName', value: 'attributeId' }"
                value-key="attributeId"
                placeholder="请选择上级类型"
                check-strictly
              ></el-tree-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="value">
              <el-input v-model="attributeDate.attributeValue" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规则">
              <el-input v-model="attributeDate.treeRule" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序">
              <el-input-number
                v-model="attributeDate.sort"
                controls-position="right"
                :min="0"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="级别">
              <el-select v-model="attributeDate.level" placeholder="请选择级别">
                <el-option
                  v-for="item in levelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitHandler">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { addData, getDataTreeList, updateData } from '../../api/dataStore'
import { getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'

const { proxy } = getCurrentInstance()
const emit = defineEmits(['submit'])

const levelOptions = [
  { label: '一级', value: 1 },
  { label: '二级', value: 2 },
  { label: '三级', value: 3 }
]

const title = ref('')
const dialogVisible = ref(false)

const attributeDate = ref({})

const treeOptions = ref([])

/** 查询菜单下拉树结构 */
async function getTreeselect() {
  treeOptions.value = []
  const res = await getDataTreeList()
  const option = { attributeId: '0', attributeName: '根目录', children: [] }
  option.children = proxy.handleTree(res.data, 'attributeId', 'attributePid')
  treeOptions.value.push(option)

  console.log('treeOptions:', treeOptions.value)
}

async function submitHandler() {
  try {
    const isUpdate = !!attributeDate.value.attributeId
    const handler = isUpdate ? updateData : addData

    const res = await handler(attributeDate.value)

    if (res.code === 200) {
      ElMessage.success(isUpdate ? '更新成功' : '添加成功')
      emit('submit')
      dialogVisible.value = false
    } else {
      ElMessage.error(res.message || '操作失败')
    }
  } catch (error) {
    console.error('请求异常:', error)
    ElMessage.error('请求出错，请稍后再试')
  }
}

function setVisible(
  titleText,
  query = {
    attributeName: '',
    attributeLabel: '',
    attributePid: '',
    attributeValue: '',
    treeRule: '',
    sort: 0,
    level: 1
  }
) {
  getTreeselect()
  title.value = titleText
  attributeDate.value = query
  dialogVisible.value = true
  treeOptions.value = []
}

defineExpose({
  setVisible
})
</script>

<style scoped lang="scss">
.dialog {
}
</style>
