<template>
  <div class="data-store app-container">
    <div class="first-type">
      <template v-for="(item, index) in firstTypes" :key="index">
        <div
          :class="['item', { active: currentIndex === index }]"
          @click="changeCurrent(item, index)"
        >
          {{ item.attributeName }}
        </div>
      </template>
    </div>

    <div class="btn">
      <el-button type="primary" size="large" @click="dataImportHandler">数据导入</el-button>
    </div>

    <div class="data-container">
      <el-tabs
        v-model="currentSecondId"
        type="border-card"
        v-if="currentSecondTypes.length"
        @tab-change="getTableData"
      >
        <template v-for="item in currentSecondTypes" :key="item.attributeId">
          <el-tab-pane :label="item.attributeName" :name="item.attributeId">
            <WyTable
              :tableData="tableData"
              :propList="currentTableConfig"
              :showIndexColumn="true"
              border
            >
              <template #handler="{ row }">
                <el-button type="primary" link @click="editRow(row)">编辑</el-button>
                <el-popconfirm
                  class="box-item"
                  title="确认要删除吗"
                  placement="top"
                  @confirm="deleteRow(row)"
                >
                  <template #reference>
                    <el-button type="danger" link>删除</el-button>
                  </template>
                </el-popconfirm>
              </template>
            </WyTable>
          </el-tab-pane>
        </template>
      </el-tabs>
      <div v-else>
        <WyTable
          :tableData="tableData"
          :propList="currentTableConfig"
          :showIndexColumn="true"
          border
        >
          <template #handler="{ row }">
            <el-button type="primary" link @click="editRow(row)">编辑</el-button>
            <el-popconfirm
              class="box-item"
              title="确认要删除吗"
              placement="top"
              @confirm="deleteRow(row)"
            >
              <template #reference>
                <el-button type="danger" link>删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </WyTable>
      </div>
    </div>

    <data-import ref="dataImportRef" @getData="getTableData"></data-import>
    <edit-dialog ref="editDialogRef" @getData="getTableData" />
  </div>
</template>

<script setup>
import { storeToRefs } from 'pinia'
import { useDataStore } from '@/store/modules/dataStore'
import WyTable from '@/components/Table/index.vue'
import DataImport from './dataImport.vue'
import EditDialog from './editDialog.vue'
import { deleteTableData, getTableDetail, getTableList } from '@/api/dataStore'

import { debounce } from 'lodash-es'
import { ElMessage } from 'element-plus'

const dataStore = useDataStore()

const { firstTypes, currentId, currentTableConfig, currentSecondTypes, currentSecondId } =
  storeToRefs(dataStore)
const currentIndex = ref(0)
const tableData = ref([])

const dataImportRef = ref(null)

const editDialogRef = ref(null)

// 获取所有类别
dataStore.getDataListAction().then(() => {
  getTableData()
})

const getTableData = debounce(async () => {
  const typeGroup = currentSecondTypes.value.length > 0 ? currentSecondId.value : currentId.value

  const res = await getTableList({ typeGroup })

  res.rows.forEach((item) => {
    const validProperties = JSON.parse(item.validProperties)
    for (const key in validProperties) {
      item[key] = validProperties[key]
    }
  })

  tableData.value = res.rows
}, 100)

function changeCurrent(item, index) {
  currentIndex.value = index
  currentId.value = item.attributeId
  getTableData()
}

function dataImportHandler() {
  dataImportRef.value.setVisible()
}

async function editRow(row) {
  const res = await getTableDetail(row.id)

  const validProperties = JSON.parse(res.data.validProperties)
  const params = {}

  for (const key in validProperties) {
    params[key] = validProperties[key]
  }
  params.id = res.data.id
  editDialogRef.value.setVisible(params)
}

async function deleteRow(row) {
  const res = await deleteTableData(row.id)
  if (res.code == 200) {
    ElMessage.success('删除成功')
    getTableData()
  }
}
</script>

<style scoped lang="scss">
.data-store {
  .first-type {
    display: flex;
    .item {
      width: 200px;
      height: 50px;
      line-height: 50px;
      text-align: center;
      background-color: #f5f5f5;
      border: 1px solid #ddd;
      border-radius: 20px;
      margin-right: 10px;
      cursor: pointer;
      &:hover {
        color: #ee7e31;
      }
      &.active {
        &:hover {
          color: white;
        }
      }
    }
    .active {
      background-color: #ee7e31;
      color: white;
      border-color: #ee7e31;
    }
  }
  .btn {
    margin-top: 20px;
  }
  .data-container {
    margin-top: 20px;
  }
}
</style>
