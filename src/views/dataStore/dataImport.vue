<template>
  <div class="dataImport">
    <el-dialog title="数据导入" v-model="dialogVisible">
      <h1>文件上传</h1>
      <file-upload-custom
        v-model="fileList"
        accept=".zip,.gdb,.mdb,.dwg,.shp"
        :fileType="['zip', 'gdb', 'mdb', 'dwg', 'shp']"
        :onUpload="uploadHandler"
      />
      <div v-if="tableData.length">
        <h1>提取数据预览</h1>
        <WyTable :tableData="tableData" :propList="config" :showIndexColumn="true" border />
      </div>
      <div v-if="tableAttr.length && tableData.length">
        <h1>字段映射</h1>
        <transfer />
      </div>
      <div v-if="mapData.length">
        <h1>已建立的映射关系</h1>
        <div class="map-box">
          <template v-for="item in mapData">
            <div class="box-item">
              {{ item.leftLabel }} --> {{ item.rightLabel }}
              <el-button link type="danger" size="small" @click="deleteHandler(item)">
                删除
              </el-button>
            </div>
          </template>
        </div>
      </div>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitHandler">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FileUploadCustom from '@/components/FileUploadCustom/index.vue'
import { addImportData, getDataList, uploadData } from '@/api/dataStore'
import WyTable from '@/components/Table/index.vue'
import transfer from './transfer.vue'
import { useDataStore } from '@/store/modules/dataStore'
import { storeToRefs } from 'pinia'
import { ElMessage } from 'element-plus'

const emit = defineEmits(['getData'])

const dataStore = useDataStore()
const { currentId, currentSecondId, currentSecondTypes, tableAttr, attribute, mapData } =
  storeToRefs(dataStore)

const dialogVisible = ref(false)

const config = ref([])
const tableData = ref([])
const fileList = ref([])
const fileId = ref('')

async function getMapList() {
  const res = await getDataList(
    currentSecondTypes.value.length ? currentSecondId.value : currentId.value
  )
  res.data.forEach((item) => {
    item.isDisabled = false
  })
  tableAttr.value = res.data
}

async function uploadHandler(file) {
  const formdata = new FormData()
  formdata.append('file', file)
  const res = await uploadData(formdata)
  config.value = constructTableConfig(res.data.attribute[0])
  tableData.value = res.data.attribute
  fileId.value = res.data.fileId

  for (const key in res.data.attribute[0]) {
    attribute.value.push({
      attributeLabel: key,
      attributeName: res.data.attribute[0][key],
      isDisabled: false
    })
  }
  getMapList()
  return res
}

// 构建table config数据
function constructTableConfig(data) {
  const config = []
  for (const key in data) {
    config.push({
      prop: key,
      label: key,
      minWidth: '100',
      slotName: key,
      align: 'center'
    })
  }
  return config
}

function deleteHandler(item) {
  dataStore.useValueFindDataAction(item.leftValue, false)
  dataStore.useValueFindDataAction(item.rightValue, false, 'attr')

  mapData.value = mapData.value.filter((data) => data.leftLabel !== item.leftLabel)
}

async function submitHandler() {
  const definition = {}
  mapData.value.forEach((item) => {
    definition[item.leftValue] = item.rightValue
  })

  const params = {
    typeGroup: currentSecondTypes.value.length
      ? `${currentId.value},${currentSecondId.value}`
      : currentId.value,
    fileId: fileId.value,
    definition: JSON.stringify(definition)
  }
  const res = await addImportData(params)
  if (res.code == 200) {
    ElMessage.success('导入成功')
    emit('getData')
    dialogVisible.value = false
  }
}

function setVisible() {
  fileList.value = []
  mapData.value = []
  tableData.value = []
  tableAttr.value = []
  attribute.value = []
  dialogVisible.value = true
}
defineExpose({
  setVisible
})
</script>

<style scoped lang="scss">
.dataImport {
  .map-box {
    padding: 10px 20px;
    border: 1px solid #d7d7d7;
    border-radius: 10px;
    .box-item {
      margin-bottom: 4px;
    }
  }
}
</style>
