<template>
  <div class="transfer">
    <div class="left">
      <div class="title">{{ leftTitle }}</div>
      <div class="content">
        <div class="input">
          <el-input v-model="leftKey" placeholder="搜索关键词"></el-input>
        </div>
        <div class="container">
          <el-checkbox-group v-model="leftCheckList" @change="changeHandler">
            <template v-for="item in leftArr" :key="item">
              <el-checkbox
                style="width: 80%"
                :label="item.attributeName"
                :value="item.attributeLabel"
                :disabled="item.isDisabled"
              />
            </template>
          </el-checkbox-group>
        </div>
      </div>
    </div>

    <div class="btn">
      <el-button type="primary" :disabled="isDisabled" @click="associationHandler">关联</el-button>
    </div>

    <div class="right">
      <div class="title">{{ rightTitle }}</div>
      <div class="content">
        <div class="input">
          <el-input v-model="rightKey" placeholder="搜索关键词"></el-input>
        </div>
        <div class="container">
          <el-checkbox-group v-model="rightCheckList" @change="changeHandler('attr')">
            <template v-for="item in rightArr" :key="item">
              <el-checkbox
                style="width: 100%"
                :label="item.attributeName"
                :value="item.attributeLabel"
                :disabled="item.isDisabled"
              />
            </template>
          </el-checkbox-group>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useDataStore } from '@/store/modules/dataStore'
import { storeToRefs } from 'pinia'
import { computed } from 'vue'
import { ref } from 'vue'

const props = defineProps({
  leftTitle: {
    type: String,
    default: '系统字段'
  },
  rightTitle: {
    type: String,
    default: '提取数据字段'
  }
})

const dataStore = useDataStore()
const { tableAttr, leftCheckList, attribute, rightCheckList, mapData } = storeToRefs(dataStore)

const leftKey = ref('')
const rightKey = ref('')

const leftArr = computed(() => {
  return tableAttr.value.filter((item) => item.attributeName.includes(leftKey.value))
})
const rightArr = computed(() => {
  return attribute.value.filter((item) => item.attributeName.includes(rightKey.value))
})

const isDisabled = computed(() => {
  return !leftCheckList.value.length || !rightCheckList.value.length
})

function changeHandler(type) {
  if (type === 'attr') {
    if (rightCheckList.value.length > 1) {
      rightCheckList.value.shift()
    }
  } else {
    if (leftCheckList.value.length > 1) {
      leftCheckList.value.shift()
    }
  }
}

// function useValueFindData(value, type) {
//   if (type === 'attr') {
//     attribute.value.forEach((item) => {
//       if (item.attributeLabel === value) {
//         item.isDisabled = true
//       }
//     })
//   } else {
//     tableAttr.value.forEach((item) => {
//       if (item.attributeLabel === value) {
//         item.isDisabled = true
//       }
//     })
//   }
// }

function associationHandler() {
  console.log(leftCheckList.value, rightCheckList.value)

  const leftValue = leftCheckList.value[0]
  const rightValue = rightCheckList.value[0]

  dataStore.useValueFindDataAction(leftValue, true)
  dataStore.useValueFindDataAction(rightValue, true, 'attr')

  const leftData = tableAttr.value.find((item) => item.attributeLabel == leftValue)
  const rightData = attribute.value.find((item) => item.attributeLabel == rightValue)

  mapData.value.push({
    leftLabel: leftData.attributeName,
    leftValue: leftData.attributeLabel,
    rightLabel: rightData.attributeName,
    rightValue: rightData.attributeLabel
  })

  leftCheckList.value = []
  rightCheckList.value = []
}
</script>

<style scoped lang="scss">
.transfer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .left,
  .right {
    width: 40%;
    border: 1px solid #d7d7d7;
    .title {
      padding: 10px 20px;
      color: #333;
      font-size: 16px;
      border-bottom: 1px solid #d7d7d7;
      background-color: #f2f2f2;
    }
    .content {
      padding: 10px;
      .container {
        width: 100%;
        height: 200px;
        overflow-y: auto;
      }
    }
  }
}
</style>
