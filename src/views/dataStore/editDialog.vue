<template>
  <div class="edit-dialog">
    <el-dialog title="编辑" v-model="dialogVisible">
      <el-form v-model="editForm" label-width="100px">
        <el-row :gutter="20">
          <template v-for="item in currentThirdTypes">
            <el-col :span="12">
              <el-form-item :label="item.attributeName">
                <el-input v-model="editForm[item.attributeLabel]"></el-input>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitHandler">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { updateTableData } from '@/api/dataStore'
import { useDataStore } from '@/store/modules/dataStore'
import { ElMessage } from 'element-plus'
import { storeToRefs } from 'pinia'
const emit = defineEmits(['getData'])
const dataStore = useDataStore()
const { currentThirdTypes } = storeToRefs(dataStore)

const dialogVisible = ref(false)
const editForm = ref({})

async function submitHandler() {
  const id = editForm.value.id
  const validProperties = { ...editForm.value }
  delete validProperties.id

  const params = {
    id,
    validProperties: JSON.stringify(validProperties)
  }
  const res = await updateTableData(params)
  if (res.code == 200) {
    ElMessage.success('更新成功')
    emit('getData')
    dialogVisible.value = false
  }
}

function setVisible(params) {
  dialogVisible.value = true
  editForm.value = params
}
defineExpose({
  setVisible
})
</script>

<style scoped lang="scss">
.edit-dialog {
}
</style>
