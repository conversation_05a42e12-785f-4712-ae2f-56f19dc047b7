<template>
  <div class="form-test-page">
    <div class="page-header">
      <h1>简化后的动态表单组件测试</h1>
      <p>使用 el-row/el-col 布局，简化了不常用属性和事件处理</p>
    </div>

    <!-- 配置控制面板 -->
    <el-card class="config-panel">
      <template #header>
        <span>配置控制面板</span>
      </template>

      <div class="config-controls">
        <el-button @click="loadDepartmentOptions"> 加载部门选项 </el-button>
        <el-button @click="loadBrandOptions"> 加载品牌选项 </el-button>
        <el-button @click="loadTagOptions"> 加载标签选项 </el-button>
        <el-button @click="clearAllOptions"> 清空动态选项 </el-button>
        <el-button @click="refreshConfigs"> 刷新配置 </el-button>
      </div>

      <div class="option-status">
        <el-tag v-if="dynamicOptions.departmentOptions?.length" type="success">
          部门选项已加载 ({{ dynamicOptions.departmentOptions.length }})
        </el-tag>
        <el-tag v-if="dynamicOptions.brandOptions?.length" type="success">
          品牌选项已加载 ({{ dynamicOptions.brandOptions.length }})
        </el-tag>
        <el-tag v-if="dynamicOptions.tagOptions?.length" type="success">
          标签选项已加载 ({{ dynamicOptions.tagOptions.length }})
        </el-tag>
      </div>
    </el-card>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>用户信息表单</span>
              <el-button size="small" @click="toggleUserFormType"> 切换表单类型 </el-button>
            </div>
          </template>

          <DynamicForm
            ref="userFormRef"
            v-model="currentFormData"
            :config="currentFormConfig"
            @change="handleUserFormChange"
          />

          <div class="form-actions">
            <el-button type="primary" @click="submitUserForm"> 提交 </el-button>
            <el-button @click="resetUserForm"> 重置 </el-button>
            <el-button @click="fillUserTestData"> 填充测试数据 </el-button>
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card>
          <template #header>
            <span>表单数据</span>
          </template>

          <div class="data-display">
            <div class="form-type-indicator">
              <el-tag type="primary">当前表单类型: {{ currentFormType }}</el-tag>
            </div>
            <pre>{{ JSON.stringify(currentFormData, null, 2) }}</pre>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>产品配置表单</span>
          </template>

          <DynamicForm
            ref="productFormRef"
            v-model="productFormData"
            :config="productFormConfig"
            @change="handleProductFormChange"
          >
            <!-- 自定义插槽示例 -->
            <template #image-upload>
              <el-upload
                class="image-uploader"
                action="https://jsonplaceholder.typicode.com/posts/"
                :show-file-list="false"
                :on-success="handleImageSuccess"
                :before-upload="beforeImageUpload"
              >
                <img v-if="imageUrl" :src="imageUrl" class="image" />
                <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
              </el-upload>
            </template>
          </DynamicForm>

          <div class="form-actions">
            <el-button type="primary" @click="submitProductForm"> 保存产品 </el-button>
            <el-button @click="resetProductForm"> 重置 </el-button>
          </div>

          <div class="data-display">
            <h4>产品数据：</h4>
            <pre>{{ JSON.stringify(productFormData, null, 2) }}</pre>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import DynamicForm from '@/components/Form/index.vue'
import {
  getUserFormConfig,
  getProductFormConfig,
  getOrderFormConfig,
  getSearchFormConfig,
  defaultOptions
} from '@/config/form-configs.js'

// 动态选项数据
const dynamicOptions = reactive({
  departmentOptions: [],
  brandOptions: [],
  tagOptions: [],
  customerOptions: [],
  productOptions: [],
  customerLoading: false
})

// 当前表单类型
const currentFormType = ref('user') // user, order, search

// 用户表单配置 - 使用计算属性实现动态配置
const userFormConfig = computed(() => {
  return getUserFormConfig({
    ...defaultOptions,
    ...dynamicOptions
  })
})

// 产品表单配置 - 使用计算属性实现动态配置
const productFormConfig = computed(() => {
  return getProductFormConfig({
    ...defaultOptions,
    ...dynamicOptions
  })
})

// 订单表单配置
const orderFormConfig = computed(() => {
  return getOrderFormConfig({
    ...defaultOptions,
    ...dynamicOptions,
    searchCustomer: searchCustomer,
    customerLoading: dynamicOptions.customerLoading
  })
})

// 搜索表单配置
const searchFormConfig = computed(() => {
  return getSearchFormConfig({
    ...defaultOptions,
    ...dynamicOptions
  })
})

// 当前表单数据的计算属性
const currentFormData = computed({
  get() {
    switch (currentFormType.value) {
      case 'user':
        return userFormData.value
      case 'order':
        return orderFormData.value
      case 'search':
        return searchFormData.value
      default:
        return userFormData.value
    }
  },
  set(value) {
    switch (currentFormType.value) {
      case 'user':
        userFormData.value = value
        break
      case 'order':
        orderFormData.value = value
        break
      case 'search':
        searchFormData.value = value
        break
      default:
        userFormData.value = value
    }
  }
})

// 当前表单配置的计算属性
const currentFormConfig = computed(() => {
  switch (currentFormType.value) {
    case 'user':
      return userFormConfig.value
    case 'order':
      return orderFormConfig.value
    case 'search':
      return searchFormConfig.value
    default:
      return userFormConfig.value
  }
})

// 表单数据
const userFormData = ref({})
const productFormData = ref({})
const orderFormData = ref({})
const searchFormData = ref({})

// 表单引用
const userFormRef = ref()
const productFormRef = ref()

// 图片上传相关
const imageUrl = ref('')

// 模拟API调用 - 搜索客户
const searchCustomer = (query) => {
  if (!query) return

  dynamicOptions.customerLoading = true

  // 模拟异步搜索
  setTimeout(() => {
    dynamicOptions.customerOptions = [
      { label: `客户A - ${query}`, value: 'customer_a' },
      { label: `客户B - ${query}`, value: 'customer_b' },
      { label: `客户C - ${query}`, value: 'customer_c' }
    ]
    dynamicOptions.customerLoading = false
  }, 500)
}

// 动态加载选项的方法
const loadDepartmentOptions = () => {
  // 模拟从API加载部门数据
  setTimeout(() => {
    dynamicOptions.departmentOptions = [
      { label: '技术部', value: 'tech' },
      { label: '产品部', value: 'product' },
      { label: '运营部', value: 'operation' },
      { label: '市场部', value: 'marketing' },
      { label: '人事部', value: 'hr' },
      { label: '财务部', value: 'finance' },
      { label: '客服部', value: 'service' },
      { label: '设计部', value: 'design' }
    ]
    ElMessage.success('部门选项加载成功')
  }, 300)
}

const loadBrandOptions = () => {
  // 模拟从API加载品牌数据
  setTimeout(() => {
    dynamicOptions.brandOptions = [
      { label: '苹果', value: 'apple' },
      { label: '华为', value: 'huawei' },
      { label: '小米', value: 'xiaomi' },
      { label: '三星', value: 'samsung' },
      { label: '联想', value: 'lenovo' },
      { label: 'OPPO', value: 'oppo' },
      { label: 'vivo', value: 'vivo' }
    ]
    ElMessage.success('品牌选项加载成功')
  }, 400)
}

const loadTagOptions = () => {
  // 模拟从API加载标签数据
  setTimeout(() => {
    dynamicOptions.tagOptions = [
      { label: '热销', value: 'hot' },
      { label: '新品', value: 'new' },
      { label: '促销', value: 'sale' },
      { label: '推荐', value: 'recommend' },
      { label: '限量', value: 'limited' },
      { label: '精选', value: 'featured' },
      { label: '爆款', value: 'bestseller' }
    ]
    ElMessage.success('标签选项加载成功')
  }, 200)
}

const clearAllOptions = () => {
  dynamicOptions.departmentOptions = []
  dynamicOptions.brandOptions = []
  dynamicOptions.tagOptions = []
  dynamicOptions.customerOptions = []
  dynamicOptions.productOptions = []
  ElMessage.info('所有动态选项已清空')
}

const refreshConfigs = () => {
  // 强制刷新配置（计算属性会自动重新计算）
  ElMessage.success('配置已刷新')
}

const toggleUserFormType = () => {
  const types = ['user', 'order', 'search']
  const currentIndex = types.indexOf(currentFormType.value)
  const nextIndex = (currentIndex + 1) % types.length
  currentFormType.value = types[nextIndex]

  ElMessage.info(`已切换到${types[nextIndex]}表单`)
}

// 事件处理
const handleUserFormChange = (data) => {
  console.log('用户表单变化:', data)
}

const handleProductFormChange = (data) => {
  console.log('产品表单变化:', data)
}

// 用户表单操作
const submitUserForm = async () => {
  try {
    const valid = await userFormRef.value.validate()
    if (valid) {
      ElMessage.success('用户信息提交成功！')
      console.log('用户数据:', userFormData.value)
    }
  } catch (error) {
    ElMessage.error('表单验证失败')
  }
}

const resetUserForm = () => {
  userFormRef.value.resetFields()
  ElMessage.info('用户表单已重置')
}

const fillUserTestData = () => {
  userFormData.value = {
    username: 'testuser',
    email: '<EMAIL>',
    age: 25,
    gender: 'male',
    status: 'active',
    interests: ['reading', 'sports'],
    birthday: '1998-01-01',
    isVip: true
  }
  ElMessage.success('测试数据已填充')
}

// 产品表单操作
const submitProductForm = async () => {
  try {
    const valid = await productFormRef.value.validate()
    if (valid) {
      ElMessage.success('产品信息保存成功！')
      console.log('产品数据:', productFormData.value)
    }
  } catch (error) {
    ElMessage.error('表单验证失败')
  }
}

const resetProductForm = () => {
  productFormRef.value.resetFields()
  imageUrl.value = ''
  ElMessage.info('产品表单已重置')
}

// 图片上传处理
const handleImageSuccess = (response, file) => {
  imageUrl.value = URL.createObjectURL(file.raw)
  productFormData.value.image = response
  ElMessage.success('图片上传成功')
}

const beforeImageUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}
</script>

<style scoped lang="scss">
.form-test-page {
  padding: 20px;

  .config-panel {
    margin-bottom: 20px;

    .config-controls {
      margin-bottom: 15px;

      .el-button {
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }

    .option-status {
      .el-tag {
        margin-right: 10px;
        margin-bottom: 5px;
      }
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .form-type-indicator {
    margin-bottom: 10px;
    text-align: center;
  }

  .page-header {
    margin-bottom: 20px;

    h1 {
      margin: 0 0 10px 0;
      color: #303133;
    }

    p {
      margin: 0;
      color: #606266;
    }
  }

  .form-actions {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;

    .el-button {
      margin-right: 10px;
    }
  }

  .data-display {
    margin-top: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
    max-height: 300px;
    overflow-y: auto;

    h4 {
      margin: 0 0 10px 0;
      color: #606266;
    }

    pre {
      margin: 0;
      font-size: 12px;
      color: #303133;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }

  .image-uploader {
    .image {
      width: 178px;
      height: 178px;
      display: block;
    }
  }
}

:deep(.image-uploader .el-upload) {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

:deep(.image-uploader .el-upload:hover) {
  border-color: var(--el-color-primary);
}

:deep(.el-icon.image-uploader-icon) {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}
</style>
