<template>
  <div class="form-test-page">
    <div class="page-header">
      <h1>动态表单组件示例</h1>
      <p>展示各种表单布局和 span 属性控制的使用方法</p>
    </div>

    <!-- 搜索表单示例 - 展示内联布局和 span 属性控制 -->
    <el-card class="form-demo-card">
      <template #header>
        <span>搜索表单示例 (inline: true + span 控制宽度)</span>
      </template>

      <!--
        配置要点：
        1. inline: true - 启用内联布局
        2. span: 6 - 每个字段占6列，一行显示4个
        3. props: { style: { width: 'XXXpx' } } - 精确控制组件宽度
        4. gutter: 16 - 设置栅格间隔
      -->
      <DynamicForm
        v-model="searchFormData"
        :config="searchFormConfig"
        @change="handleSearchChange"
      />

      <div class="form-actions">
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </div>

      <div class="data-display">
        <h4>搜索表单数据：</h4>
        <pre>{{ JSON.stringify(searchFormData, null, 2) }}</pre>
      </div>
    </el-card>

    <!-- 用户表单演示 - 展示栅格布局和常用 span 组合 -->
    <el-card class="form-demo-card">
      <template #header>
        <span>用户表单演示 (栅格布局 + span: 12/8 组合)</span>
      </template>

      <!--
        配置要点：
        1. 不设置 inline，使用默认的栅格布局
        2. span: 12 - 半宽字段，一行两个 (用户名、邮箱)
        3. span: 8 - 三分之一宽字段，一行三个 (年龄、性别、状态等)
        4. gutter: 20 - 栅格间隔
        5. 支持表单验证规则和各种组件类型
        6. 展示了最常用的两种 span 布局组合
      -->
      <DynamicForm v-model="userFormData" :config="userFormConfig" @change="handleUserFormChange" />

      <div class="form-actions">
        <el-button type="primary" @click="submitUserForm">提交用户表单</el-button>
        <el-button @click="resetUserForm">重置</el-button>
      </div>

      <div class="data-display">
        <h4>用户表单数据：</h4>
        <pre>{{ JSON.stringify(userFormData, null, 2) }}</pre>
      </div>
    </el-card>

    <!-- 产品表单演示 - 展示不同宽度组合和文本域全宽布局 -->
    <el-card class="form-demo-card">
      <template #header>
        <span>产品表单演示 (多种 span 组合)</span>
      </template>

      <!--
        配置要点：
        1. span: 12 - 产品名称半宽
        2. span: 24 - 文本域全宽，独占一行
        3. span: 8 - 价格、分类、品牌三分之一宽，一行三个
        4. 展示了如何合理安排不同类型字段的布局
      -->
      <DynamicForm
        v-model="productFormData"
        :config="productFormConfig"
        @change="handleProductFormChange"
      />

      <div class="form-actions">
        <el-button type="primary" @click="submitProductForm">提交产品表单</el-button>
        <el-button @click="resetProductForm">重置</el-button>
      </div>

      <div class="data-display">
        <h4>产品表单数据：</h4>
        <pre>{{ JSON.stringify(productFormData, null, 2) }}</pre>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import DynamicForm from '@/components/Form/index.vue'
import {
  getUserFormConfig,
  getProductFormConfig,
  getSearchFormConfig,
  defaultOptions
} from '@/config/form-configs.js'

// 表单数据
const searchFormData = ref({})
const userFormData = ref({})
const productFormData = ref({})

// 动态选项
const dynamicOptions = reactive({
  categoryOptions: [
    { label: '电子产品', value: 'electronics' },
    { label: '服装鞋帽', value: 'clothing' },
    { label: '食品饮料', value: 'food' },
    { label: '图书文具', value: 'books' },
    { label: '家居用品', value: 'home' }
  ],
  statusOptions: [
    { label: '启用', value: 'active' },
    { label: '禁用', value: 'inactive' },
    { label: '待审核', value: 'pending' },
    { label: '已删除', value: 'deleted' }
  ],
  departmentOptions: [
    { label: '技术部', value: 'tech' },
    { label: '产品部', value: 'product' },
    { label: '运营部', value: 'operation' },
    { label: '市场部', value: 'marketing' }
  ],
  brandOptions: [
    { label: '苹果', value: 'apple' },
    { label: '华为', value: 'huawei' },
    { label: '小米', value: 'xiaomi' },
    { label: '三星', value: 'samsung' }
  ],
  regionOptions: [
    {
      label: '广东省',
      value: 'guangdong',
      children: [
        { label: '广州市', value: 'guangzhou' },
        { label: '深圳市', value: 'shenzhen' }
      ]
    },
    {
      label: '北京市',
      value: 'beijing',
      children: [
        { label: '朝阳区', value: 'chaoyang' },
        { label: '海淀区', value: 'haidian' }
      ]
    }
  ]
})

// 修复后的搜索表单配置（带宽度设置）
const searchFormConfig = getSearchFormConfig({
  ...defaultOptions,
  ...dynamicOptions
})

// 用户表单配置
const userFormConfig = getUserFormConfig({
  ...defaultOptions,
  ...dynamicOptions
})

// 产品表单配置
const productFormConfig = getProductFormConfig({
  ...defaultOptions,
  ...dynamicOptions
})

// 事件处理
const handleSearchChange = (data) => {
  console.log('搜索表单变化:', data)
}

const handleSearch = () => {
  console.log('执行搜索:', searchFormData.value)
  ElMessage.success('搜索执行成功！')
}

const handleReset = () => {
  searchFormData.value = {}
  ElMessage.info('搜索表单已重置')
}

// 用户表单事件处理
const handleUserFormChange = (data) => {
  console.log('用户表单变化:', data)
}

const submitUserForm = () => {
  console.log('提交用户表单:', userFormData.value)
  ElMessage.success('用户表单提交成功！')
}

const resetUserForm = () => {
  userFormData.value = {}
  ElMessage.info('用户表单已重置')
}

// 产品表单事件处理
const handleProductFormChange = (data) => {
  console.log('产品表单变化:', data)
}

const submitProductForm = () => {
  console.log('提交产品表单:', productFormData.value)
  ElMessage.success('产品表单提交成功！')
}

const resetProductForm = () => {
  productFormData.value = {}
  ElMessage.info('产品表单已重置')
}
</script>

<style scoped lang="scss">
.form-test-page {
  padding: 20px;

  .page-header {
    margin-bottom: 30px;
    text-align: center;

    h1 {
      color: #303133;
      margin: 0 0 10px 0;
      font-size: 24px;
    }

    p {
      color: #606266;
      margin: 0;
      font-size: 14px;
    }
  }

  .form-demo-card {
    margin-bottom: 30px;

    .form-actions {
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid #ebeef5;
      text-align: center;

      .el-button {
        margin: 0 10px;
      }
    }

    .data-display {
      margin-top: 20px;
      background-color: #f5f7fa;
      padding: 15px;
      border-radius: 4px;

      h4 {
        margin: 0 0 10px 0;
        color: #909399;
        font-size: 14px;
      }

      pre {
        margin: 0;
        font-size: 12px;
        color: #303133;
        white-space: pre-wrap;
        word-break: break-all;
        max-height: 200px;
        overflow-y: auto;
      }
    }
  }
}
</style>
