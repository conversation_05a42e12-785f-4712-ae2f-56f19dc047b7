<template>
  <div class="search-form-test">
    <h2>搜索表单宽度测试</h2>

    <el-card>
      <template #header>
        <span>搜索表单 - 修复前后对比</span>
      </template>

      <div class="form-section">
        <h3>修复后的搜索表单（带宽度设置）</h3>
        <DynamicForm
          v-model="searchFormData"
          :config="searchFormConfig"
          @change="handleSearchChange"
        />

        <div class="form-actions">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </div>
      </div>

      <el-divider />

      <div class="form-section">
        <h3>对比：没有宽度设置的内联表单</h3>
        <DynamicForm
          v-model="searchFormData2"
          :config="searchFormConfigNoWidth"
          @change="handleSearchChange2"
        />
      </div>

      <el-divider />

      <div class="data-section">
        <h3>表单数据</h3>
        <div class="data-display">
          <h4>修复后表单数据：</h4>
          <pre>{{ JSON.stringify(searchFormData, null, 2) }}</pre>

          <h4>对比表单数据：</h4>
          <pre>{{ JSON.stringify(searchFormData2, null, 2) }}</pre>
        </div>
      </div>
    </el-card>

    <!-- 用户表单演示 -->
    <el-card class="form-demo-card">
      <template #header>
        <span>用户表单演示 (span 布局控制)</span>
      </template>

      <DynamicForm v-model="userFormData" :config="userFormConfig" @change="handleUserFormChange" />

      <div class="form-actions">
        <el-button type="primary" @click="submitUserForm">提交用户表单</el-button>
        <el-button @click="resetUserForm">重置</el-button>
      </div>

      <div class="data-display">
        <h4>用户表单数据：</h4>
        <pre>{{ JSON.stringify(userFormData, null, 2) }}</pre>
      </div>
    </el-card>

    <!-- 产品表单演示 -->
    <el-card class="form-demo-card">
      <template #header>
        <span>产品表单演示 (混合 span 布局)</span>
      </template>

      <DynamicForm
        v-model="productFormData"
        :config="productFormConfig"
        @change="handleProductFormChange"
      />

      <div class="form-actions">
        <el-button type="primary" @click="submitProductForm">提交产品表单</el-button>
        <el-button @click="resetProductForm">重置</el-button>
      </div>

      <div class="data-display">
        <h4>产品表单数据：</h4>
        <pre>{{ JSON.stringify(productFormData, null, 2) }}</pre>
      </div>
    </el-card>

    <!-- 布局演示表单 -->
    <el-card class="form-demo-card">
      <template #header>
        <span>布局演示表单 (各种 span 组合)</span>
      </template>

      <DynamicForm
        v-model="layoutDemoFormData"
        :config="layoutDemoFormConfig"
        @change="handleLayoutDemoFormChange"
      />

      <div class="form-actions">
        <el-button type="primary" @click="submitLayoutDemoForm">提交布局演示表单</el-button>
        <el-button @click="resetLayoutDemoForm">重置</el-button>
      </div>

      <div class="data-display">
        <h4>布局演示表单数据：</h4>
        <pre>{{ JSON.stringify(layoutDemoFormData, null, 2) }}</pre>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import DynamicForm from '@/components/Form/index.vue'
import {
  getUserFormConfig,
  getProductFormConfig,
  getSearchFormConfig,
  getLayoutDemoFormConfig,
  defaultOptions
} from '@/config/form-configs.js'

// 表单数据
const searchFormData = ref({})
const searchFormData2 = ref({})
const userFormData = ref({})
const productFormData = ref({})
const layoutDemoFormData = ref({})

// 动态选项
const dynamicOptions = reactive({
  categoryOptions: [
    { label: '电子产品', value: 'electronics' },
    { label: '服装鞋帽', value: 'clothing' },
    { label: '食品饮料', value: 'food' },
    { label: '图书文具', value: 'books' },
    { label: '家居用品', value: 'home' }
  ],
  statusOptions: [
    { label: '启用', value: 'active' },
    { label: '禁用', value: 'inactive' },
    { label: '待审核', value: 'pending' },
    { label: '已删除', value: 'deleted' }
  ],
  departmentOptions: [
    { label: '技术部', value: 'tech' },
    { label: '产品部', value: 'product' },
    { label: '运营部', value: 'operation' },
    { label: '市场部', value: 'marketing' }
  ],
  brandOptions: [
    { label: '苹果', value: 'apple' },
    { label: '华为', value: 'huawei' },
    { label: '小米', value: 'xiaomi' },
    { label: '三星', value: 'samsung' }
  ],
  regionOptions: [
    {
      label: '广东省',
      value: 'guangdong',
      children: [
        { label: '广州市', value: 'guangzhou' },
        { label: '深圳市', value: 'shenzhen' }
      ]
    },
    {
      label: '北京市',
      value: 'beijing',
      children: [
        { label: '朝阳区', value: 'chaoyang' },
        { label: '海淀区', value: 'haidian' }
      ]
    }
  ]
})

// 修复后的搜索表单配置（带宽度设置）
const searchFormConfig = getSearchFormConfig({
  ...defaultOptions,
  ...dynamicOptions
})

// 用户表单配置
const userFormConfig = getUserFormConfig({
  ...defaultOptions,
  ...dynamicOptions
})

// 产品表单配置
const productFormConfig = getProductFormConfig({
  ...defaultOptions,
  ...dynamicOptions
})

// 布局演示表单配置
const layoutDemoFormConfig = getLayoutDemoFormConfig({
  ...defaultOptions,
  ...dynamicOptions
})

// 对比用的搜索表单配置（不带宽度设置）
const searchFormConfigNoWidth = {
  labelWidth: '80px',
  inline: true,
  items: [
    {
      type: 'input',
      prop: 'keyword',
      label: '关键词',
      placeholder: '请输入关键词',
      clearable: true
      // 没有 props: { style: { width: '200px' } }
    },
    {
      type: 'select',
      prop: 'category',
      label: '分类',
      placeholder: '请选择分类',
      clearable: true,
      options: dynamicOptions.categoryOptions
      // 没有 props: { style: { width: '150px' } }
    },
    {
      type: 'select',
      prop: 'status',
      label: '状态',
      placeholder: '请选择状态',
      clearable: true,
      options: dynamicOptions.statusOptions
      // 没有 props: { style: { width: '120px' } }
    },
    {
      type: 'date',
      prop: 'dateRange',
      label: '日期',
      dateType: 'daterange',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
      // 没有 props: { style: { width: '240px' } }
    }
  ]
}

// 事件处理
const handleSearchChange = (data) => {
  console.log('搜索表单变化（修复后）:', data)
}

const handleSearchChange2 = (data) => {
  console.log('搜索表单变化（对比）:', data)
}

const handleSearch = () => {
  console.log('执行搜索:', searchFormData.value)
  ElMessage.success('搜索执行成功！')
}

const handleReset = () => {
  searchFormData.value = {}
  searchFormData2.value = {}
  ElMessage.info('表单已重置')
}

// 用户表单事件处理
const handleUserFormChange = (data) => {
  console.log('用户表单变化:', data)
}

const submitUserForm = () => {
  console.log('提交用户表单:', userFormData.value)
  ElMessage.success('用户表单提交成功！')
}

const resetUserForm = () => {
  userFormData.value = {}
  ElMessage.info('用户表单已重置')
}

// 产品表单事件处理
const handleProductFormChange = (data) => {
  console.log('产品表单变化:', data)
}

const submitProductForm = () => {
  console.log('提交产品表单:', productFormData.value)
  ElMessage.success('产品表单提交成功！')
}

const resetProductForm = () => {
  productFormData.value = {}
  ElMessage.info('产品表单已重置')
}

// 布局演示表单事件处理
const handleLayoutDemoFormChange = (data) => {
  console.log('布局演示表单变化:', data)
}

const submitLayoutDemoForm = () => {
  console.log('提交布局演示表单:', layoutDemoFormData.value)
  ElMessage.success('布局演示表单提交成功！')
}

const resetLayoutDemoForm = () => {
  layoutDemoFormData.value = {}
  ElMessage.info('布局演示表单已重置')
}
</script>

<style scoped lang="scss">
.search-form-test {
  padding: 20px;

  h2 {
    color: #409eff;
    margin-bottom: 20px;
    text-align: center;
  }

  .form-section {
    margin-bottom: 20px;

    h3 {
      color: #606266;
      margin-bottom: 15px;
      font-size: 16px;
    }
  }

  .form-actions {
    margin-top: 20px;
    text-align: center;

    .el-button {
      margin: 0 10px;
    }
  }

  .data-section {
    h3 {
      color: #606266;
      margin-bottom: 15px;
      font-size: 16px;
    }
  }

  .data-display {
    background-color: #f5f7fa;
    padding: 15px;
    border-radius: 4px;

    h4 {
      margin: 10px 0;
      color: #909399;
      font-size: 14px;
    }

    pre {
      margin: 0;
      font-size: 12px;
      color: #303133;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
}

// 突出显示宽度差异
:deep(.el-form--inline .el-form-item) {
  margin-right: 15px;
  margin-bottom: 10px;
}

// 新增表单演示卡片样式
.form-demo-card {
  margin-bottom: 30px;

  .form-actions {
    margin: 20px 0;
    text-align: center;

    .el-button {
      margin: 0 10px;
    }
  }

  .data-display {
    background: #f5f7fa;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
    margin-top: 20px;

    h4 {
      color: #909399;
      margin: 0 0 10px 0;
      font-size: 14px;
      font-weight: normal;
    }

    pre {
      background: #ffffff;
      padding: 10px;
      border-radius: 4px;
      border: 1px solid #dcdfe6;
      font-size: 12px;
      color: #606266;
      margin: 0;
      white-space: pre-wrap;
      word-wrap: break-word;
      max-height: 300px;
      overflow-y: auto;
    }
  }
}
</style>
