<template>
  <div class="form-test-page">
    <div class="page-header">
      <h1>动态表单组件测试</h1>
      <p>这是一个测试页面，用于验证动态表单组件的功能</p>
    </div>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>用户信息表单</span>
          </template>
          
          <DynamicForm
            ref="userFormRef"
            v-model="userFormData"
            :config="userFormConfig"
            @change="handleUserFormChange"
          />
          
          <div class="form-actions">
            <el-button type="primary" @click="submitUserForm">
              提交
            </el-button>
            <el-button @click="resetUserForm">
              重置
            </el-button>
            <el-button @click="fillUserTestData">
              填充测试数据
            </el-button>
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card>
          <template #header>
            <span>表单数据</span>
          </template>
          
          <div class="data-display">
            <pre>{{ JSON.stringify(userFormData, null, 2) }}</pre>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>产品配置表单</span>
          </template>
          
          <DynamicForm
            ref="productFormRef"
            v-model="productFormData"
            :config="productFormConfig"
            @change="handleProductFormChange"
          >
            <!-- 自定义插槽示例 -->
            <template #image-upload="{ item, formData }">
              <el-upload
                class="image-uploader"
                action="https://jsonplaceholder.typicode.com/posts/"
                :show-file-list="false"
                :on-success="handleImageSuccess"
                :before-upload="beforeImageUpload"
              >
                <img v-if="imageUrl" :src="imageUrl" class="image" />
                <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
              </el-upload>
            </template>
          </DynamicForm>
          
          <div class="form-actions">
            <el-button type="primary" @click="submitProductForm">
              保存产品
            </el-button>
            <el-button @click="resetProductForm">
              重置
            </el-button>
          </div>
          
          <div class="data-display">
            <h4>产品数据：</h4>
            <pre>{{ JSON.stringify(productFormData, null, 2) }}</pre>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import DynamicForm from '@/components/Form/index.vue'

// 用户表单配置
const userFormConfig = reactive({
  labelWidth: '100px',
  items: [
    {
      type: 'input',
      prop: 'username',
      label: '用户名',
      placeholder: '请输入用户名',
      required: true,
      clearable: true,
      rules: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
      ]
    },
    {
      type: 'input',
      prop: 'email',
      label: '邮箱',
      placeholder: '请输入邮箱',
      inputType: 'email',
      clearable: true,
      rules: [
        { required: true, message: '请输入邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ]
    },
    {
      type: 'number',
      prop: 'age',
      label: '年龄',
      min: 1,
      max: 120,
      placeholder: '请输入年龄'
    },
    {
      type: 'select',
      prop: 'gender',
      label: '性别',
      placeholder: '请选择性别',
      options: [
        { label: '男', value: 'male' },
        { label: '女', value: 'female' },
        { label: '其他', value: 'other' }
      ]
    },
    {
      type: 'radio',
      prop: 'status',
      label: '状态',
      options: [
        { label: '活跃', value: 'active' },
        { label: '非活跃', value: 'inactive' }
      ],
      defaultValue: 'active'
    },
    {
      type: 'checkbox',
      prop: 'interests',
      label: '兴趣',
      options: [
        { label: '阅读', value: 'reading' },
        { label: '运动', value: 'sports' },
        { label: '音乐', value: 'music' },
        { label: '旅行', value: 'travel' }
      ]
    },
    {
      type: 'date',
      prop: 'birthday',
      label: '生日',
      dateType: 'date',
      placeholder: '请选择生日',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    },
    {
      type: 'switch',
      prop: 'isVip',
      label: 'VIP用户',
      activeText: '是',
      inactiveText: '否'
    }
  ]
})

// 产品表单配置
const productFormConfig = reactive({
  labelWidth: '120px',
  items: [
    {
      type: 'input',
      prop: 'name',
      label: '产品名称',
      placeholder: '请输入产品名称',
      required: true,
      rules: [{ required: true, message: '请输入产品名称', trigger: 'blur' }]
    },
    {
      type: 'textarea',
      prop: 'description',
      label: '产品描述',
      placeholder: '请输入产品描述',
      rows: 3,
      maxlength: 500,
      showWordLimit: true
    },
    {
      type: 'number',
      prop: 'price',
      label: '价格',
      min: 0,
      precision: 2,
      placeholder: '请输入价格'
    },
    {
      type: 'select',
      prop: 'category',
      label: '分类',
      placeholder: '请选择分类',
      options: [
        { label: '电子产品', value: 'electronics' },
        { label: '服装', value: 'clothing' },
        { label: '食品', value: 'food' },
        { label: '图书', value: 'books' }
      ]
    },
    {
      type: 'cascader',
      prop: 'region',
      label: '销售区域',
      placeholder: '请选择销售区域',
      options: [
        {
          value: 'china',
          label: '中国',
          children: [
            {
              value: 'beijing',
              label: '北京',
              children: [
                { value: 'chaoyang', label: '朝阳区' },
                { value: 'haidian', label: '海淀区' }
              ]
            },
            {
              value: 'shanghai',
              label: '上海',
              children: [
                { value: 'huangpu', label: '黄浦区' },
                { value: 'xuhui', label: '徐汇区' }
              ]
            }
          ]
        }
      ]
    },
    {
      type: 'slider',
      prop: 'quality',
      label: '质量评分',
      min: 0,
      max: 100,
      showInput: true,
      defaultValue: 80
    },
    {
      type: 'rate',
      prop: 'rating',
      label: '用户评分',
      max: 5,
      allowHalf: true,
      showText: true,
      texts: ['极差', '失望', '一般', '满意', '惊喜']
    },
    {
      type: 'color',
      prop: 'themeColor',
      label: '主题色',
      showAlpha: false,
      predefine: [
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585'
      ]
    },
    {
      type: 'slot',
      prop: 'image',
      label: '产品图片',
      slotName: 'image-upload'
    }
  ]
})

// 表单数据
const userFormData = ref({})
const productFormData = ref({})

// 表单引用
const userFormRef = ref()
const productFormRef = ref()

// 图片上传相关
const imageUrl = ref('')

// 事件处理
const handleUserFormChange = (data) => {
  console.log('用户表单变化:', data)
}

const handleProductFormChange = (data) => {
  console.log('产品表单变化:', data)
}

// 用户表单操作
const submitUserForm = async () => {
  try {
    const valid = await userFormRef.value.validate()
    if (valid) {
      ElMessage.success('用户信息提交成功！')
      console.log('用户数据:', userFormData.value)
    }
  } catch (error) {
    ElMessage.error('表单验证失败')
  }
}

const resetUserForm = () => {
  userFormRef.value.resetFields()
  ElMessage.info('用户表单已重置')
}

const fillUserTestData = () => {
  userFormData.value = {
    username: 'testuser',
    email: '<EMAIL>',
    age: 25,
    gender: 'male',
    status: 'active',
    interests: ['reading', 'sports'],
    birthday: '1998-01-01',
    isVip: true
  }
  ElMessage.success('测试数据已填充')
}

// 产品表单操作
const submitProductForm = async () => {
  try {
    const valid = await productFormRef.value.validate()
    if (valid) {
      ElMessage.success('产品信息保存成功！')
      console.log('产品数据:', productFormData.value)
    }
  } catch (error) {
    ElMessage.error('表单验证失败')
  }
}

const resetProductForm = () => {
  productFormRef.value.resetFields()
  imageUrl.value = ''
  ElMessage.info('产品表单已重置')
}

// 图片上传处理
const handleImageSuccess = (response, file) => {
  imageUrl.value = URL.createObjectURL(file.raw)
  productFormData.value.image = response
  ElMessage.success('图片上传成功')
}

const beforeImageUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}
</script>

<style scoped lang="scss">
.form-test-page {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    h1 {
      margin: 0 0 10px 0;
      color: #303133;
    }
    
    p {
      margin: 0;
      color: #606266;
    }
  }
  
  .form-actions {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;
    
    .el-button {
      margin-right: 10px;
    }
  }
  
  .data-display {
    margin-top: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
    max-height: 300px;
    overflow-y: auto;
    
    h4 {
      margin: 0 0 10px 0;
      color: #606266;
    }
    
    pre {
      margin: 0;
      font-size: 12px;
      color: #303133;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
  
  .image-uploader {
    .image {
      width: 178px;
      height: 178px;
      display: block;
    }
  }
}

:deep(.image-uploader .el-upload) {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

:deep(.image-uploader .el-upload:hover) {
  border-color: var(--el-color-primary);
}

:deep(.el-icon.image-uploader-icon) {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}
</style>
