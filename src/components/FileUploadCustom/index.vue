<template>
  <div class="file-upload-custom" v-loading="loading" element-loading-text="文件上传中...">
    <el-upload
      ref="uploadRef"
      drag
      action="#"
      :limit="limit"
      :accept="accept"
      :disabled="isDisabled || currentStatus"
      :auto-upload="autoUpload"
      :show-file-list="false"
      :file-list="fileList"
      :on-exceed="exceedHandler"
      :before-upload="beforeUpload"
      :http-request="httpRequestHandler"
    >
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text"><em>点击</em>或者拖拽文件到此处上传</div>
      <div class="el-upload__text">（支持{{ fileType.join('、') }}）</div>
    </el-upload>
  </div>
</template>

<script setup>
import { UploadFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'

const props = defineProps({
  modelValue: [String, Object, Array],
  limit: {
    type: Number,
    default: 1
  },
  accept: {
    type: String,
    default: '.zip'
  },
  autoUpload: {
    type: Boolean,
    default: true
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ['zip']
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 0
  },
  isDisabled: {
    type: Boolean,
    default: false
  },
  onUpload: {
    type: Function,
    required: true
  }
})

// 2. 定义 emit
const emit = defineEmits(['update:modelValue'])

// 3. 创建一个响应式 computed 属性，用来在模板中使用
const fileList = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const uploadRef = ref(null)
// const fileList = ref([])
const currentStatus = ref(false)
const loading = ref(false)

function exceedHandler(files) {
  ElMessage.warning('只能上传一个文件，已替换旧文件')
  fileList.value = []

  const file = files[0]
  beforeUpload(file)
  httpRequestHandler({
    file,
    fileList: [],
    onSuccess: (result, file) => {
      fileList.value = [file]
    }
  })
}

// 可选：上传前的校验，比如限制文件大小、类型
function beforeUpload(file) {
  // 校检文件类型
  if (props.fileType.length) {
    const fileName = file.name.split('.')
    const fileExt = fileName[fileName.length - 1]
    const isTypeOk = props.fileType.indexOf(fileExt) >= 0
    if (!isTypeOk) {
      ElMessage.error(`文件格式不正确，请上传${props.fileType.join('/')}格式文件!`)
      return false
    }
  }
  // 校检文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize
    if (!isLt) {
      ElMessage.error(`上传文件大小不能超过 ${props.fileSize} MB!`)
      return false
    }
  }
  loading.value = true

  return true
}

async function httpRequestHandler({ file, fileList, onSuccess }) {
  try {
    currentStatus.value = true
    const result = await props.onUpload(file)
    if (result.code === 200) {
      onSuccess(result, file, fileList)
    }
    currentStatus.value = false
    loading.value = false
  } catch {
    currentStatus.value = false
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.file-upload-custom {
}
</style>
