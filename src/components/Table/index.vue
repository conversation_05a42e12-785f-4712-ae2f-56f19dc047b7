<template>
  <div class="tableCmp">
    <el-table
      :data="tableData"
      :max-height="height"
      :border="border"
      :cell-class-name="cellClassName"
      @selection-change="selectHandle"
    >
      <el-table-column
        v-if="showSelectColumn"
        type="selection"
        width="55"
        align="center"
        label="全选"
      >
      </el-table-column>
      <el-table-column
        v-if="showIndexColumn"
        label="序号"
        type="index"
        align="center"
        width="60"
      ></el-table-column>
      <el-table-column v-for="item in propList" :key="item.prop" v-bind="item">
        <template #default="scope">
          <slot :name="item.slotName" :row="scope.row">
            {{ scope.row[item.prop] }}
          </slot>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
defineProps({
  border: { type: Boolean, default: false },
  tableData: { type: Array, default: () => [] },
  propList: { type: Array, default: () => [] },
  showSelectColumn: { type: Boolean, default: false },
  showIndexColumn: { type: Boolean, default: false },
  height: { type: Number, default: null },
  cellClassName: { type: Function, default: null }
})

const emit = defineEmits(['selectChange'])
function selectHandle(row) {
  emit('selectChange', row)
}
</script>

<style lang="scss" scoped>
.tableCmp {
  color: red;
}
</style>
