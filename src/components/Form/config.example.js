// 动态表单配置示例
export const formConfig = {
  // 表单全局配置
  labelWidth: '120px',
  labelPosition: 'right',
  size: 'default',
  disabled: false,
  inline: false,
  
  // 表单项配置
  items: [
    // 输入框
    {
      type: 'input',
      prop: 'username',
      label: '用户名',
      placeholder: '请输入用户名',
      required: true,
      clearable: true,
      maxlength: 20,
      showWordLimit: true,
      rules: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
      ],
      onChange: (value, formData) => {
        console.log('用户名变化:', value)
      }
    },
    
    // 密码输入框
    {
      type: 'input',
      prop: 'password',
      label: '密码',
      placeholder: '请输入密码',
      required: true,
      showPassword: true,
      clearable: true,
      rules: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
      ]
    },
    
    // 邮箱输入框
    {
      type: 'input',
      prop: 'email',
      label: '邮箱',
      placeholder: '请输入邮箱地址',
      inputType: 'email',
      clearable: true,
      rules: [
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ]
    },
    
    // 数字输入框
    {
      type: 'number',
      prop: 'age',
      label: '年龄',
      placeholder: '请输入年龄',
      min: 1,
      max: 120,
      step: 1,
      controls: true,
      rules: [
        { type: 'number', message: '年龄必须为数字值', trigger: 'blur' }
      ]
    },
    
    // 文本域
    {
      type: 'textarea',
      prop: 'description',
      label: '个人描述',
      placeholder: '请输入个人描述',
      rows: 4,
      maxlength: 500,
      showWordLimit: true,
      resize: 'vertical'
    },
    
    // 选择器
    {
      type: 'select',
      prop: 'gender',
      label: '性别',
      placeholder: '请选择性别',
      clearable: true,
      options: [
        { label: '男', value: 'male' },
        { label: '女', value: 'female' },
        { label: '其他', value: 'other' }
      ],
      rules: [
        { required: true, message: '请选择性别', trigger: 'change' }
      ]
    },
    
    // 多选选择器
    {
      type: 'select',
      prop: 'hobbies',
      label: '兴趣爱好',
      placeholder: '请选择兴趣爱好',
      multiple: true,
      clearable: true,
      collapseTags: true,
      collapseTagsTooltip: true,
      maxCollapseTags: 2,
      options: [
        { label: '阅读', value: 'reading' },
        { label: '运动', value: 'sports' },
        { label: '音乐', value: 'music' },
        { label: '旅行', value: 'travel' },
        { label: '摄影', value: 'photography' },
        { label: '编程', value: 'programming' }
      ]
    },
    
    // 单选框组
    {
      type: 'radio',
      prop: 'status',
      label: '状态',
      options: [
        { label: '启用', value: 'active' },
        { label: '禁用', value: 'inactive' },
        { label: '待审核', value: 'pending' }
      ],
      defaultValue: 'active'
    },
    
    // 多选框组
    {
      type: 'checkbox',
      prop: 'permissions',
      label: '权限',
      options: [
        { label: '查看', value: 'view' },
        { label: '编辑', value: 'edit' },
        { label: '删除', value: 'delete' },
        { label: '管理', value: 'admin' }
      ],
      min: 1,
      max: 3
    },
    
    // 日期选择器
    {
      type: 'date',
      prop: 'birthday',
      label: '生日',
      dateType: 'date',
      placeholder: '请选择生日',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      clearable: true
    },
    
    // 日期范围选择器
    {
      type: 'date',
      prop: 'workPeriod',
      label: '工作时间',
      dateType: 'daterange',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      rangeSeparator: '至',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    },
    
    // 时间选择器
    {
      type: 'time',
      prop: 'workTime',
      label: '工作时间',
      placeholder: '请选择时间',
      format: 'HH:mm:ss',
      valueFormat: 'HH:mm:ss'
    },
    
    // 开关
    {
      type: 'switch',
      prop: 'isVip',
      label: 'VIP用户',
      activeText: '是',
      inactiveText: '否',
      activeValue: true,
      inactiveValue: false
    },
    
    // 滑块
    {
      type: 'slider',
      prop: 'score',
      label: '评分',
      min: 0,
      max: 100,
      step: 1,
      showInput: true,
      showStops: false,
      showTooltip: true
    },
    
    // 评分
    {
      type: 'rate',
      prop: 'rating',
      label: '满意度',
      max: 5,
      allowHalf: true,
      showText: true,
      texts: ['极差', '失望', '一般', '满意', '惊喜']
    },
    
    // 颜色选择器
    {
      type: 'color',
      prop: 'favoriteColor',
      label: '喜欢的颜色',
      showAlpha: false,
      colorFormat: 'hex',
      predefine: [
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585'
      ]
    },
    
    // 级联选择器
    {
      type: 'cascader',
      prop: 'region',
      label: '所在地区',
      placeholder: '请选择地区',
      clearable: true,
      showAllLevels: true,
      options: [
        {
          value: 'beijing',
          label: '北京',
          children: [
            { value: 'chaoyang', label: '朝阳区' },
            { value: 'haidian', label: '海淀区' },
            { value: 'dongcheng', label: '东城区' }
          ]
        },
        {
          value: 'shanghai',
          label: '上海',
          children: [
            { value: 'huangpu', label: '黄浦区' },
            { value: 'xuhui', label: '徐汇区' },
            { value: 'changning', label: '长宁区' }
          ]
        }
      ]
    },
    
    // 自定义插槽
    {
      type: 'slot',
      prop: 'custom',
      label: '自定义内容',
      slotName: 'custom-slot'
    }
  ]
}

// 表单验证规则示例
export const customRules = {
  // 自定义验证规则
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  
  idCard: [
    { required: true, message: '请输入身份证号', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请输入身份证号'))
        } else if (!/^\d{17}[\dXx]$/.test(value)) {
          callback(new Error('请输入正确的身份证号'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
}

// 简单表单配置示例
export const simpleFormConfig = {
  labelWidth: '100px',
  items: [
    {
      type: 'input',
      prop: 'name',
      label: '姓名',
      required: true,
      rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
    },
    {
      type: 'select',
      prop: 'department',
      label: '部门',
      options: [
        { label: '技术部', value: 'tech' },
        { label: '产品部', value: 'product' },
        { label: '运营部', value: 'operation' }
      ]
    },
    {
      type: 'date',
      prop: 'joinDate',
      label: '入职日期',
      dateType: 'date'
    }
  ]
}
