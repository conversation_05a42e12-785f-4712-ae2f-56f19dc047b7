<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    :label-width="config.labelWidth || '120px'"
    :label-position="config.labelPosition || 'right'"
    :size="config.size || 'default'"
    :disabled="config.disabled || false"
    :inline="config.inline || false"
    v-bind="config.formProps || {}"
  >
    <template v-for="item in config.items" :key="item.prop">
      <!-- 输入框 -->
      <el-form-item
        v-if="item.type === 'input'"
        :label="item.label"
        :prop="item.prop"
        :rules="item.rules"
        :required="item.required"
        v-bind="item.formItemProps || {}"
      >
        <el-input
          v-model="formData[item.prop]"
          :placeholder="item.placeholder || `请输入${item.label}`"
          :disabled="item.disabled"
          :readonly="item.readonly"
          :clearable="item.clearable !== false"
          :show-password="item.showPassword"
          :type="item.inputType || 'text'"
          :maxlength="item.maxlength"
          :minlength="item.minlength"
          :show-word-limit="item.showWordLimit"
          :prefix-icon="item.prefixIcon"
          :suffix-icon="item.suffixIcon"
          v-bind="item.props || {}"
          @input="handleInput(item, $event)"
          @change="handleChange(item, $event)"
          @blur="handleBlur(item, $event)"
          @focus="handleFocus(item, $event)"
        />
      </el-form-item>

      <!-- 文本域 -->
      <el-form-item
        v-else-if="item.type === 'textarea'"
        :label="item.label"
        :prop="item.prop"
        :rules="item.rules"
        :required="item.required"
        v-bind="item.formItemProps || {}"
      >
        <el-input
          v-model="formData[item.prop]"
          type="textarea"
          :placeholder="item.placeholder || `请输入${item.label}`"
          :disabled="item.disabled"
          :readonly="item.readonly"
          :rows="item.rows || 3"
          :maxlength="item.maxlength"
          :minlength="item.minlength"
          :show-word-limit="item.showWordLimit"
          :resize="item.resize"
          v-bind="item.props || {}"
          @input="handleInput(item, $event)"
          @change="handleChange(item, $event)"
          @blur="handleBlur(item, $event)"
          @focus="handleFocus(item, $event)"
        />
      </el-form-item>

      <!-- 数字输入框 -->
      <el-form-item
        v-else-if="item.type === 'number'"
        :label="item.label"
        :prop="item.prop"
        :rules="item.rules"
        :required="item.required"
        v-bind="item.formItemProps || {}"
      >
        <el-input-number
          v-model="formData[item.prop]"
          :placeholder="item.placeholder || `请输入${item.label}`"
          :disabled="item.disabled"
          :min="item.min"
          :max="item.max"
          :step="item.step || 1"
          :precision="item.precision"
          :controls="item.controls !== false"
          :controls-position="item.controlsPosition"
          v-bind="item.props || {}"
          @change="handleChange(item, $event)"
          @blur="handleBlur(item, $event)"
          @focus="handleFocus(item, $event)"
        />
      </el-form-item>

      <!-- 选择器 -->
      <el-form-item
        v-else-if="item.type === 'select'"
        :label="item.label"
        :prop="item.prop"
        :rules="item.rules"
        :required="item.required"
        v-bind="item.formItemProps || {}"
      >
        <el-select
          v-model="formData[item.prop]"
          :placeholder="item.placeholder || `请选择${item.label}`"
          :disabled="item.disabled"
          :clearable="item.clearable !== false"
          :multiple="item.multiple"
          :filterable="item.filterable"
          :remote="item.remote"
          :remote-method="item.remoteMethod"
          :loading="item.loading"
          :collapse-tags="item.collapseTags"
          :collapse-tags-tooltip="item.collapseTagsTooltip"
          :max-collapse-tags="item.maxCollapseTags"
          v-bind="item.props || {}"
          @change="handleChange(item, $event)"
          @visible-change="handleVisibleChange(item, $event)"
          @remove-tag="handleRemoveTag(item, $event)"
          @clear="handleClear(item)"
          @blur="handleBlur(item, $event)"
          @focus="handleFocus(item, $event)"
        >
          <el-option
            v-for="option in item.options"
            :key="option.value"
            :label="option.label"
            :value="option.value"
            :disabled="option.disabled"
          />
        </el-select>
      </el-form-item>

      <!-- 单选框组 -->
      <el-form-item
        v-else-if="item.type === 'radio'"
        :label="item.label"
        :prop="item.prop"
        :rules="item.rules"
        :required="item.required"
        v-bind="item.formItemProps || {}"
      >
        <el-radio-group
          v-model="formData[item.prop]"
          :disabled="item.disabled"
          :size="item.size"
          v-bind="item.props || {}"
          @change="handleChange(item, $event)"
        >
          <el-radio
            v-for="option in item.options"
            :key="option.value"
            :value="option.value"
            :disabled="option.disabled"
            :border="item.border"
          >
            {{ option.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 多选框组 -->
      <el-form-item
        v-else-if="item.type === 'checkbox'"
        :label="item.label"
        :prop="item.prop"
        :rules="item.rules"
        :required="item.required"
        v-bind="item.formItemProps || {}"
      >
        <el-checkbox-group
          v-model="formData[item.prop]"
          :disabled="item.disabled"
          :size="item.size"
          :min="item.min"
          :max="item.max"
          v-bind="item.props || {}"
          @change="handleChange(item, $event)"
        >
          <el-checkbox
            v-for="option in item.options"
            :key="option.value"
            :value="option.value"
            :disabled="option.disabled"
            :border="item.border"
          >
            {{ option.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <!-- 日期选择器 -->
      <el-form-item
        v-else-if="item.type === 'date'"
        :label="item.label"
        :prop="item.prop"
        :rules="item.rules"
        :required="item.required"
        v-bind="item.formItemProps || {}"
      >
        <el-date-picker
          v-model="formData[item.prop]"
          :type="item.dateType || 'date'"
          :placeholder="item.placeholder || `请选择${item.label}`"
          :disabled="item.disabled"
          :readonly="item.readonly"
          :clearable="item.clearable !== false"
          :size="item.size"
          :editable="item.editable !== false"
          :format="item.format"
          :value-format="item.valueFormat"
          :start-placeholder="item.startPlaceholder"
          :end-placeholder="item.endPlaceholder"
          :range-separator="item.rangeSeparator || '至'"
          :default-value="item.defaultValue"
          :default-time="item.defaultTime"
          :disabled-date="item.disabledDate"
          :shortcuts="item.shortcuts"
          v-bind="item.props || {}"
          @change="handleChange(item, $event)"
          @blur="handleBlur(item, $event)"
          @focus="handleFocus(item, $event)"
        />
      </el-form-item>

      <!-- 时间选择器 -->
      <el-form-item
        v-else-if="item.type === 'time'"
        :label="item.label"
        :prop="item.prop"
        :rules="item.rules"
        :required="item.required"
        v-bind="item.formItemProps || {}"
      >
        <el-time-picker
          v-model="formData[item.prop]"
          :placeholder="item.placeholder || `请选择${item.label}`"
          :disabled="item.disabled"
          :readonly="item.readonly"
          :clearable="item.clearable !== false"
          :size="item.size"
          :editable="item.editable !== false"
          :format="item.format"
          :value-format="item.valueFormat"
          :start-placeholder="item.startPlaceholder"
          :end-placeholder="item.endPlaceholder"
          :range-separator="item.rangeSeparator || '至'"
          :is-range="item.isRange"
          :arrow-control="item.arrowControl"
          :disabled-hours="item.disabledHours"
          :disabled-minutes="item.disabledMinutes"
          :disabled-seconds="item.disabledSeconds"
          v-bind="item.props || {}"
          @change="handleChange(item, $event)"
          @blur="handleBlur(item, $event)"
          @focus="handleFocus(item, $event)"
        />
      </el-form-item>

      <!-- 开关 -->
      <el-form-item
        v-else-if="item.type === 'switch'"
        :label="item.label"
        :prop="item.prop"
        :rules="item.rules"
        :required="item.required"
        v-bind="item.formItemProps || {}"
      >
        <el-switch
          v-model="formData[item.prop]"
          :disabled="item.disabled"
          :size="item.size"
          :width="item.width"
          :inline-prompt="item.inlinePrompt"
          :active-icon="item.activeIcon"
          :inactive-icon="item.inactiveIcon"
          :active-text="item.activeText"
          :inactive-text="item.inactiveText"
          :active-value="item.activeValue !== undefined ? item.activeValue : true"
          :inactive-value="item.inactiveValue !== undefined ? item.inactiveValue : false"
          :active-color="item.activeColor"
          :inactive-color="item.inactiveColor"
          :border-color="item.borderColor"
          v-bind="item.props || {}"
          @change="handleChange(item, $event)"
        />
      </el-form-item>

      <!-- 滑块 -->
      <el-form-item
        v-else-if="item.type === 'slider'"
        :label="item.label"
        :prop="item.prop"
        :rules="item.rules"
        :required="item.required"
        v-bind="item.formItemProps || {}"
      >
        <el-slider
          v-model="formData[item.prop]"
          :disabled="item.disabled"
          :min="item.min || 0"
          :max="item.max || 100"
          :step="item.step || 1"
          :show-input="item.showInput"
          :show-input-controls="item.showInputControls !== false"
          :size="item.size"
          :input-size="item.inputSize"
          :show-stops="item.showStops"
          :show-tooltip="item.showTooltip !== false"
          :format-tooltip="item.formatTooltip"
          :range="item.range"
          :vertical="item.vertical"
          :height="item.height"
          :label="item.sliderLabel"
          :debounce="item.debounce || 300"
          :tooltip-class="item.tooltipClass"
          :marks="item.marks"
          v-bind="item.props || {}"
          @change="handleChange(item, $event)"
          @input="handleInput(item, $event)"
        />
      </el-form-item>

      <!-- 评分 -->
      <el-form-item
        v-else-if="item.type === 'rate'"
        :label="item.label"
        :prop="item.prop"
        :rules="item.rules"
        :required="item.required"
        v-bind="item.formItemProps || {}"
      >
        <el-rate
          v-model="formData[item.prop]"
          :disabled="item.disabled"
          :size="item.size"
          :max="item.max || 5"
          :allow-half="item.allowHalf"
          :low-threshold="item.lowThreshold || 2"
          :high-threshold="item.highThreshold || 4"
          :colors="item.colors"
          :void-color="item.voidColor"
          :disabled-void-color="item.disabledVoidColor"
          :icon-classes="item.iconClasses"
          :void-icon-class="item.voidIconClass"
          :disabled-void-icon-class="item.disabledVoidIconClass"
          :show-text="item.showText"
          :show-score="item.showScore"
          :text-color="item.textColor"
          :texts="item.texts"
          :score-template="item.scoreTemplate"
          v-bind="item.props || {}"
          @change="handleChange(item, $event)"
        />
      </el-form-item>

      <!-- 颜色选择器 -->
      <el-form-item
        v-else-if="item.type === 'color'"
        :label="item.label"
        :prop="item.prop"
        :rules="item.rules"
        :required="item.required"
        v-bind="item.formItemProps || {}"
      >
        <el-color-picker
          v-model="formData[item.prop]"
          :disabled="item.disabled"
          :size="item.size"
          :show-alpha="item.showAlpha"
          :color-format="item.colorFormat"
          :predefine="item.predefine"
          v-bind="item.props || {}"
          @change="handleChange(item, $event)"
          @active-change="handleActiveChange(item, $event)"
        />
      </el-form-item>

      <!-- 级联选择器 -->
      <el-form-item
        v-else-if="item.type === 'cascader'"
        :label="item.label"
        :prop="item.prop"
        :rules="item.rules"
        :required="item.required"
        v-bind="item.formItemProps || {}"
      >
        <el-cascader
          v-model="formData[item.prop]"
          :options="item.options"
          :placeholder="item.placeholder || `请选择${item.label}`"
          :disabled="item.disabled"
          :clearable="item.clearable !== false"
          :show-all-levels="item.showAllLevels !== false"
          :collapse-tags="item.collapseTags"
          :collapse-tags-tooltip="item.collapseTagsTooltip"
          :separator="item.separator || ' / '"
          :size="item.size"
          :props="item.cascaderProps"
          :filterable="item.filterable"
          :filter-method="item.filterMethod"
          v-bind="item.props || {}"
          @change="handleChange(item, $event)"
          @expand-change="handleExpandChange(item, $event)"
          @blur="handleBlur(item, $event)"
          @focus="handleFocus(item, $event)"
          @visible-change="handleVisibleChange(item, $event)"
          @remove-tag="handleRemoveTag(item, $event)"
        />
      </el-form-item>

      <!-- 自定义插槽 -->
      <el-form-item
        v-else-if="item.type === 'slot'"
        :label="item.label"
        :prop="item.prop"
        :rules="item.rules"
        :required="item.required"
        v-bind="item.formItemProps || {}"
      >
        <slot :name="item.slotName" :item="item" :formData="formData" />
      </el-form-item>
    </template>
  </el-form>
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted } from 'vue'

// 定义组件属性
const props = defineProps({
  config: {
    type: Object,
    required: true,
    default: () => ({})
  },
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

// 定义事件
const emit = defineEmits([
  'update:modelValue',
  'input',
  'change',
  'blur',
  'focus',
  'visible-change',
  'remove-tag',
  'clear',
  'active-change',
  'expand-change'
])

// 表单引用
const formRef = ref()

// 表单数据
const formData = reactive({})

// 表单验证规则
const formRules = computed(() => {
  const rules = {}
  props.config.items?.forEach((item) => {
    if (item.rules) {
      rules[item.prop] = item.rules
    }
  })
  return rules
})

// 初始化表单数据
const initFormData = () => {
  // 清空现有数据
  Object.keys(formData).forEach((key) => {
    delete formData[key]
  })

  // 设置默认值
  props.config.items?.forEach((item) => {
    if (item.defaultValue !== undefined) {
      formData[item.prop] = item.defaultValue
    } else if (item.type === 'checkbox') {
      formData[item.prop] = []
    } else {
      formData[item.prop] = ''
    }
  })

  // 合并传入的值
  Object.assign(formData, props.modelValue)
}

// 监听配置变化
watch(
  () => props.config,
  () => {
    initFormData()
  },
  { immediate: true, deep: true }
)

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newVal) => {
    Object.assign(formData, newVal)
  },
  { deep: true }
)

// 监听表单数据变化，向外发送
watch(
  formData,
  (newVal) => {
    emit('update:modelValue', { ...newVal })
  },
  { deep: true }
)

// 事件处理函数
const handleInput = (item, value) => {
  emit('input', { item, value, formData })
  item.onInput?.(value, formData)
}

const handleChange = (item, value) => {
  emit('change', { item, value, formData })
  item.onChange?.(value, formData)
}

const handleBlur = (item, event) => {
  emit('blur', { item, event, formData })
  item.onBlur?.(event, formData)
}

const handleFocus = (item, event) => {
  emit('focus', { item, event, formData })
  item.onFocus?.(event, formData)
}

const handleVisibleChange = (item, visible) => {
  emit('visible-change', { item, visible, formData })
  item.onVisibleChange?.(visible, formData)
}

const handleRemoveTag = (item, tag) => {
  emit('remove-tag', { item, tag, formData })
  item.onRemoveTag?.(tag, formData)
}

const handleClear = (item) => {
  emit('clear', { item, formData })
  item.onClear?.(formData)
}

const handleActiveChange = (item, color) => {
  emit('active-change', { item, color, formData })
  item.onActiveChange?.(color, formData)
}

const handleExpandChange = (item, value) => {
  emit('expand-change', { item, value, formData })
  item.onExpandChange?.(value, formData)
}

// 表单验证方法
const validate = (callback) => {
  return formRef.value?.validate(callback)
}

const validateField = (props, callback) => {
  return formRef.value?.validateField(props, callback)
}

const resetFields = () => {
  formRef.value?.resetFields()
}

const scrollToField = (prop) => {
  formRef.value?.scrollToField(prop)
}

const clearValidate = (props) => {
  formRef.value?.clearValidate(props)
}

// 获取表单数据
const getFormData = () => {
  return { ...formData }
}

// 设置表单数据
const setFormData = (data) => {
  Object.assign(formData, data)
}

// 暴露方法给父组件
defineExpose({
  validate,
  validateField,
  resetFields,
  scrollToField,
  clearValidate,
  getFormData,
  setFormData,
  formRef
})

// 组件挂载后初始化
onMounted(() => {
  initFormData()
})
</script>

<style scoped lang="scss">
.el-form {
  .el-form-item {
    margin-bottom: 18px;
  }
}
</style>
