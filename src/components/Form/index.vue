<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    :label-width="config.labelWidth || '120px'"
    :size="config.size || 'default'"
    :disabled="config.disabled || false"
  >
    <el-row :gutter="config.gutter || 20">
      <template v-for="item in config.items" :key="item.prop">
        <el-col :span="item.span || (config.inline ? 6 : 24)">
          <!-- 输入框 -->
          <el-form-item
            v-if="item.type === 'input'"
            :label="item.label"
            :prop="item.prop"
            :rules="item.rules"
            :required="item.required"
          >
            <el-input
              v-model="formData[item.prop]"
              :placeholder="item.placeholder || `请输入${item.label}`"
              :disabled="item.disabled"
              :clearable="item.clearable !== false"
              :show-password="item.showPassword"
              :type="item.inputType || 'text'"
              :maxlength="item.maxlength"
              v-bind="item.props || {}"
              @change="handleChange(item, $event)"
            />
          </el-form-item>

          <!-- 文本域 -->
          <el-form-item
            v-else-if="item.type === 'textarea'"
            :label="item.label"
            :prop="item.prop"
            :rules="item.rules"
            :required="item.required"
          >
            <el-input
              v-model="formData[item.prop]"
              type="textarea"
              :placeholder="item.placeholder || `请输入${item.label}`"
              :disabled="item.disabled"
              :rows="item.rows || 3"
              :maxlength="item.maxlength"
              v-bind="item.props || {}"
              @change="handleChange(item, $event)"
            />
          </el-form-item>

          <!-- 数字输入框 -->
          <el-form-item
            v-else-if="item.type === 'number'"
            :label="item.label"
            :prop="item.prop"
            :rules="item.rules"
            :required="item.required"
          >
            <el-input-number
              v-model="formData[item.prop]"
              :placeholder="item.placeholder || `请输入${item.label}`"
              :disabled="item.disabled"
              :min="item.min"
              :max="item.max"
              :step="item.step || 1"
              v-bind="item.props || {}"
              @change="handleChange(item, $event)"
            />
          </el-form-item>

          <!-- 选择器 -->
          <el-form-item
            v-else-if="item.type === 'select'"
            :label="item.label"
            :prop="item.prop"
            :rules="item.rules"
            :required="item.required"
          >
            <el-select
              v-model="formData[item.prop]"
              :placeholder="item.placeholder || `请选择${item.label}`"
              :disabled="item.disabled"
              :clearable="item.clearable !== false"
              :filterable="item.filterable"
              :multiple="item.multiple"
              v-bind="item.props || {}"
              @change="handleChange(item, $event)"
            >
              <el-option
                v-for="option in item.options"
                :key="option.value"
                :label="option.label"
                :value="option.value"
                :disabled="option.disabled"
              />
            </el-select>
          </el-form-item>

          <!-- 单选框组 -->
          <el-form-item
            v-else-if="item.type === 'radio'"
            :label="item.label"
            :prop="item.prop"
            :rules="item.rules"
            :required="item.required"
          >
            <el-radio-group
              v-model="formData[item.prop]"
              :disabled="item.disabled"
              v-bind="item.props || {}"
              @change="handleChange(item, $event)"
            >
              <el-radio
                v-for="option in item.options"
                :key="option.value"
                :value="option.value"
                :disabled="option.disabled"
              >
                {{ option.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 多选框组 -->
          <el-form-item
            v-else-if="item.type === 'checkbox'"
            :label="item.label"
            :prop="item.prop"
            :rules="item.rules"
            :required="item.required"
          >
            <el-checkbox-group
              v-model="formData[item.prop]"
              :disabled="item.disabled"
              v-bind="item.props || {}"
              @change="handleChange(item, $event)"
            >
              <el-checkbox
                v-for="option in item.options"
                :key="option.value"
                :value="option.value"
                :disabled="option.disabled"
              >
                {{ option.label }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <!-- 日期选择器 -->
          <el-form-item
            v-else-if="item.type === 'date'"
            :label="item.label"
            :prop="item.prop"
            :rules="item.rules"
            :required="item.required"
            v-bind="item.formItemProps || {}"
          >
            <el-date-picker
              v-model="formData[item.prop]"
              :type="item.dateType || 'date'"
              :placeholder="item.placeholder || `请选择${item.label}`"
              :start-placeholder="item.startPlaceholder"
              :end-placeholder="item.endPlaceholder"
              :disabled="item.disabled"
              :clearable="item.clearable !== false"
              :format="item.format"
              :value-format="item.valueFormat"
              :range-separator="item.rangeSeparator || '至'"
              v-bind="item.props || {}"
              @change="handleChange(item, $event)"
            />
          </el-form-item>

          <!-- 开关 -->
          <el-form-item
            v-else-if="item.type === 'switch'"
            :label="item.label"
            :prop="item.prop"
            :rules="item.rules"
            :required="item.required"
          >
            <el-switch
              v-model="formData[item.prop]"
              :disabled="item.disabled"
              :active-text="item.activeText"
              :inactive-text="item.inactiveText"
              :active-value="item.activeValue !== undefined ? item.activeValue : true"
              :inactive-value="item.inactiveValue !== undefined ? item.inactiveValue : false"
              v-bind="item.props || {}"
              @change="handleChange(item, $event)"
            />
          </el-form-item>

          <!-- 级联选择器 -->
          <el-form-item
            v-else-if="item.type === 'cascader'"
            :label="item.label"
            :prop="item.prop"
            :rules="item.rules"
            :required="item.required"
            v-bind="item.formItemProps || {}"
          >
            <el-cascader
              v-model="formData[item.prop]"
              :options="item.options"
              :placeholder="item.placeholder || `请选择${item.label}`"
              :disabled="item.disabled"
              :clearable="item.clearable !== false"
              :props="item.cascaderProps"
              v-bind="item.props || {}"
              @change="handleChange(item, $event)"
            />
          </el-form-item>

          <!-- 自定义插槽 -->
          <el-form-item
            v-else-if="item.type === 'slot'"
            :label="item.label"
            :prop="item.prop"
            :rules="item.rules"
            :required="item.required"
          >
            <slot :name="item.slotName" :item="item" :formData="formData" />
          </el-form-item>
        </el-col>
      </template>
    </el-row>
  </el-form>
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted } from 'vue'

// 定义组件属性
const props = defineProps({
  config: {
    type: Object,
    required: true,
    default: () => ({})
  },
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'change'])

// 表单引用
const formRef = ref()

// 表单数据
const formData = reactive({})

// 表单验证规则
const formRules = computed(() => {
  const rules = {}
  props.config.items?.forEach((item) => {
    if (item.rules) {
      rules[item.prop] = item.rules
    }
  })
  return rules
})

// 初始化表单数据
const initFormData = () => {
  // 清空现有数据
  Object.keys(formData).forEach((key) => {
    delete formData[key]
  })

  // 设置默认值
  props.config.items?.forEach((item) => {
    if (item.defaultValue !== undefined) {
      formData[item.prop] = item.defaultValue
    } else if (item.type === 'checkbox') {
      formData[item.prop] = []
    } else {
      formData[item.prop] = ''
    }
  })

  // 合并传入的值
  Object.assign(formData, props.modelValue)
}

// 监听配置变化
watch(
  () => props.config,
  () => {
    initFormData()
  },
  { immediate: true, deep: true }
)

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newVal) => {
    Object.assign(formData, newVal)
  },
  { deep: true }
)

// 监听表单数据变化，向外发送
watch(
  formData,
  (newVal) => {
    emit('update:modelValue', { ...newVal })
  },
  { deep: true }
)

// 事件处理函数
const handleChange = (item, value) => {
  emit('change', { item, value, formData })
  item.onChange?.(value, formData)
}

// 表单验证方法
const validate = (callback) => {
  return formRef.value?.validate(callback)
}

const validateField = (props, callback) => {
  return formRef.value?.validateField(props, callback)
}

const resetFields = () => {
  formRef.value?.resetFields()
}

const scrollToField = (prop) => {
  formRef.value?.scrollToField(prop)
}

const clearValidate = (props) => {
  formRef.value?.clearValidate(props)
}

// 获取表单数据
const getFormData = () => {
  return { ...formData }
}

// 设置表单数据
const setFormData = (data) => {
  Object.assign(formData, data)
}

// 暴露方法给父组件
defineExpose({
  validate,
  validateField,
  resetFields,
  scrollToField,
  clearValidate,
  getFormData,
  setFormData,
  formRef
})

// 组件挂载后初始化
onMounted(() => {
  initFormData()
})
</script>

<style scoped lang="scss">
.el-form {
  .el-form-item {
    margin-bottom: 18px;
  }
}
</style>
