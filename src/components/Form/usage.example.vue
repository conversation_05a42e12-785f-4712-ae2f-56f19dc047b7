<template>
  <div class="form-example">
    <h2>动态表单组件使用示例</h2>
    
    <!-- 基础用法 -->
    <el-card class="example-card">
      <template #header>
        <div class="card-header">
          <span>基础用法</span>
        </div>
      </template>
      
      <DynamicForm
        ref="basicFormRef"
        v-model="basicFormData"
        :config="basicConfig"
        @change="handleFormChange"
        @input="handleFormInput"
      />
      
      <div class="form-actions">
        <el-button type="primary" @click="submitBasicForm">提交</el-button>
        <el-button @click="resetBasicForm">重置</el-button>
        <el-button @click="validateBasicForm">验证</el-button>
      </div>
      
      <div class="form-data">
        <h4>表单数据：</h4>
        <pre>{{ JSON.stringify(basicFormData, null, 2) }}</pre>
      </div>
    </el-card>
    
    <!-- 复杂表单示例 -->
    <el-card class="example-card">
      <template #header>
        <div class="card-header">
          <span>复杂表单示例</span>
        </div>
      </template>
      
      <DynamicForm
        ref="complexFormRef"
        v-model="complexFormData"
        :config="complexConfig"
        @change="handleComplexFormChange"
      >
        <!-- 自定义插槽示例 -->
        <template #custom-slot="{ item, formData }">
          <div class="custom-content">
            <el-upload
              class="upload-demo"
              action="https://jsonplaceholder.typicode.com/posts/"
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :file-list="fileList"
              list-type="picture"
            >
              <el-button size="small" type="primary">点击上传</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  只能上传jpg/png文件，且不超过500kb
                </div>
              </template>
            </el-upload>
          </div>
        </template>
      </DynamicForm>
      
      <div class="form-actions">
        <el-button type="primary" @click="submitComplexForm">提交</el-button>
        <el-button @click="resetComplexForm">重置</el-button>
        <el-button @click="fillTestData">填充测试数据</el-button>
      </div>
      
      <div class="form-data">
        <h4>表单数据：</h4>
        <pre>{{ JSON.stringify(complexFormData, null, 2) }}</pre>
      </div>
    </el-card>
    
    <!-- 动态配置示例 -->
    <el-card class="example-card">
      <template #header>
        <div class="card-header">
          <span>动态配置示例</span>
        </div>
      </template>
      
      <div class="config-controls">
        <el-button @click="toggleFormDisabled">
          {{ dynamicConfig.disabled ? '启用表单' : '禁用表单' }}
        </el-button>
        <el-button @click="toggleFormInline">
          {{ dynamicConfig.inline ? '垂直布局' : '水平布局' }}
        </el-button>
        <el-button @click="addFormItem">添加表单项</el-button>
        <el-button @click="removeFormItem">移除表单项</el-button>
      </div>
      
      <DynamicForm
        ref="dynamicFormRef"
        v-model="dynamicFormData"
        :config="dynamicConfig"
      />
      
      <div class="form-data">
        <h4>表单数据：</h4>
        <pre>{{ JSON.stringify(dynamicFormData, null, 2) }}</pre>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import DynamicForm from './index.vue'

// 基础表单配置
const basicConfig = reactive({
  labelWidth: '100px',
  items: [
    {
      type: 'input',
      prop: 'name',
      label: '姓名',
      placeholder: '请输入姓名',
      required: true,
      rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
    },
    {
      type: 'select',
      prop: 'gender',
      label: '性别',
      placeholder: '请选择性别',
      options: [
        { label: '男', value: 'male' },
        { label: '女', value: 'female' }
      ],
      rules: [{ required: true, message: '请选择性别', trigger: 'change' }]
    },
    {
      type: 'number',
      prop: 'age',
      label: '年龄',
      min: 1,
      max: 120,
      rules: [{ required: true, message: '请输入年龄', trigger: 'blur' }]
    }
  ]
})

// 复杂表单配置
const complexConfig = reactive({
  labelWidth: '120px',
  items: [
    {
      type: 'input',
      prop: 'username',
      label: '用户名',
      placeholder: '请输入用户名',
      required: true,
      clearable: true,
      maxlength: 20,
      showWordLimit: true,
      rules: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
      ]
    },
    {
      type: 'input',
      prop: 'email',
      label: '邮箱',
      placeholder: '请输入邮箱',
      inputType: 'email',
      rules: [
        { required: true, message: '请输入邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ]
    },
    {
      type: 'select',
      prop: 'hobbies',
      label: '兴趣爱好',
      multiple: true,
      collapseTags: true,
      options: [
        { label: '阅读', value: 'reading' },
        { label: '运动', value: 'sports' },
        { label: '音乐', value: 'music' },
        { label: '旅行', value: 'travel' }
      ]
    },
    {
      type: 'date',
      prop: 'birthday',
      label: '生日',
      dateType: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    },
    {
      type: 'switch',
      prop: 'isVip',
      label: 'VIP用户',
      activeText: '是',
      inactiveText: '否'
    },
    {
      type: 'slider',
      prop: 'score',
      label: '评分',
      min: 0,
      max: 100,
      showInput: true
    },
    {
      type: 'slot',
      prop: 'avatar',
      label: '头像',
      slotName: 'custom-slot'
    }
  ]
})

// 动态配置
const dynamicConfig = reactive({
  labelWidth: '100px',
  disabled: false,
  inline: false,
  items: [
    {
      type: 'input',
      prop: 'field1',
      label: '字段1',
      placeholder: '请输入内容'
    },
    {
      type: 'select',
      prop: 'field2',
      label: '字段2',
      options: [
        { label: '选项1', value: 'option1' },
        { label: '选项2', value: 'option2' }
      ]
    }
  ]
})

// 表单数据
const basicFormData = ref({})
const complexFormData = ref({})
const dynamicFormData = ref({})

// 表单引用
const basicFormRef = ref()
const complexFormRef = ref()
const dynamicFormRef = ref()

// 文件列表
const fileList = ref([])

// 事件处理
const handleFormChange = (data) => {
  console.log('表单变化:', data)
}

const handleFormInput = (data) => {
  console.log('输入事件:', data)
}

const handleComplexFormChange = (data) => {
  console.log('复杂表单变化:', data)
}

// 表单操作
const submitBasicForm = async () => {
  try {
    const valid = await basicFormRef.value.validate()
    if (valid) {
      ElMessage.success('基础表单验证通过！')
      console.log('提交数据:', basicFormData.value)
    }
  } catch (error) {
    ElMessage.error('表单验证失败')
  }
}

const resetBasicForm = () => {
  basicFormRef.value.resetFields()
  ElMessage.info('基础表单已重置')
}

const validateBasicForm = async () => {
  try {
    const valid = await basicFormRef.value.validate()
    ElMessage.success(valid ? '验证通过' : '验证失败')
  } catch (error) {
    ElMessage.error('验证失败')
  }
}

const submitComplexForm = async () => {
  try {
    const valid = await complexFormRef.value.validate()
    if (valid) {
      ElMessage.success('复杂表单验证通过！')
      console.log('提交数据:', complexFormData.value)
    }
  } catch (error) {
    ElMessage.error('表单验证失败')
  }
}

const resetComplexForm = () => {
  complexFormRef.value.resetFields()
  ElMessage.info('复杂表单已重置')
}

const fillTestData = () => {
  complexFormData.value = {
    username: 'testuser',
    email: '<EMAIL>',
    hobbies: ['reading', 'music'],
    birthday: '1990-01-01',
    isVip: true,
    score: 85
  }
  ElMessage.success('测试数据已填充')
}

// 动态配置操作
const toggleFormDisabled = () => {
  dynamicConfig.disabled = !dynamicConfig.disabled
}

const toggleFormInline = () => {
  dynamicConfig.inline = !dynamicConfig.inline
}

const addFormItem = () => {
  const newField = {
    type: 'input',
    prop: `field${dynamicConfig.items.length + 1}`,
    label: `字段${dynamicConfig.items.length + 1}`,
    placeholder: '新增字段'
  }
  dynamicConfig.items.push(newField)
  ElMessage.success('已添加新字段')
}

const removeFormItem = () => {
  if (dynamicConfig.items.length > 1) {
    dynamicConfig.items.pop()
    ElMessage.success('已移除字段')
  } else {
    ElMessage.warning('至少保留一个字段')
  }
}

// 文件上传处理
const handlePreview = (file) => {
  console.log('预览文件:', file)
}

const handleRemove = (file, fileList) => {
  console.log('移除文件:', file, fileList)
}
</script>

<style scoped lang="scss">
.form-example {
  padding: 20px;
  
  .example-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .form-actions {
    margin: 20px 0;
    padding: 20px 0;
    border-top: 1px solid #ebeef5;
    
    .el-button {
      margin-right: 10px;
    }
  }
  
  .form-data {
    margin-top: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
    
    h4 {
      margin: 0 0 10px 0;
      color: #606266;
    }
    
    pre {
      margin: 0;
      font-size: 12px;
      color: #303133;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
  
  .config-controls {
    margin-bottom: 20px;
    
    .el-button {
      margin-right: 10px;
      margin-bottom: 10px;
    }
  }
  
  .custom-content {
    .upload-demo {
      .el-upload__tip {
        color: #606266;
        font-size: 12px;
        margin-top: 7px;
      }
    }
  }
}
</style>
