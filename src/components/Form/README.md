# 动态表单组件 (DynamicForm)

基于 Vue3 和 Element Plus 的动态表单组件，通过配置文件即可快速生成各种类型的表单。

## 特性

- 🚀 **开箱即用** - 只需传入配置即可生成完整表单
- 🎨 **丰富组件** - 支持输入框、选择器、日期选择器、开关等多种组件
- 📝 **表单验证** - 内置表单验证，支持自定义验证规则
- 🔧 **高度可配置** - 支持组件的所有原生属性和事件
- 🎯 **TypeScript 友好** - 完整的类型定义
- 🔌 **插槽支持** - 支持自定义插槽内容
- 📱 **响应式** - 支持响应式布局

## 安装

组件已集成在项目中，直接导入使用即可：

```javascript
import DynamicForm from '@/components/Form/index.vue'
```

## 基础用法

```vue
<template>
  <DynamicForm
    ref="formRef"
    v-model="formData"
    :config="formConfig"
    @change="handleChange"
  />
  
  <el-button @click="submitForm">提交</el-button>
</template>

<script setup>
import { ref } from 'vue'
import DynamicForm from '@/components/Form/index.vue'

const formRef = ref()
const formData = ref({})

const formConfig = {
  labelWidth: '120px',
  items: [
    {
      type: 'input',
      prop: 'name',
      label: '姓名',
      placeholder: '请输入姓名',
      required: true,
      rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
    },
    {
      type: 'select',
      prop: 'gender',
      label: '性别',
      options: [
        { label: '男', value: 'male' },
        { label: '女', value: 'female' }
      ]
    }
  ]
}

const handleChange = (data) => {
  console.log('表单数据变化:', data)
}

const submitForm = async () => {
  try {
    const valid = await formRef.value.validate()
    if (valid) {
      console.log('提交数据:', formData.value)
    }
  } catch (error) {
    console.error('验证失败:', error)
  }
}
</script>
```

## 配置说明

### 表单配置 (config)

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| labelWidth | string | '120px' | 标签宽度 |
| labelPosition | string | 'right' | 标签位置 |
| size | string | 'default' | 表单尺寸 |
| disabled | boolean | false | 是否禁用 |
| inline | boolean | false | 是否行内表单 |
| items | array | [] | 表单项配置 |

### 表单项配置 (items)

每个表单项的通用属性：

| 属性 | 类型 | 说明 |
|------|------|------|
| type | string | 组件类型 |
| prop | string | 字段名 |
| label | string | 标签文本 |
| placeholder | string | 占位符 |
| required | boolean | 是否必填 |
| disabled | boolean | 是否禁用 |
| rules | array | 验证规则 |
| defaultValue | any | 默认值 |

## 支持的组件类型

### 1. 输入框 (input)

```javascript
{
  type: 'input',
  prop: 'username',
  label: '用户名',
  placeholder: '请输入用户名',
  inputType: 'text', // text, email, url, tel 等
  maxlength: 20,
  showWordLimit: true,
  clearable: true,
  showPassword: false, // 密码输入框
  prefixIcon: 'User',
  suffixIcon: 'Search'
}
```

### 2. 文本域 (textarea)

```javascript
{
  type: 'textarea',
  prop: 'description',
  label: '描述',
  rows: 4,
  maxlength: 500,
  showWordLimit: true,
  resize: 'vertical'
}
```

### 3. 数字输入框 (number)

```javascript
{
  type: 'number',
  prop: 'age',
  label: '年龄',
  min: 0,
  max: 120,
  step: 1,
  precision: 0,
  controls: true
}
```

### 4. 选择器 (select)

```javascript
{
  type: 'select',
  prop: 'city',
  label: '城市',
  multiple: false,
  clearable: true,
  filterable: true,
  options: [
    { label: '北京', value: 'beijing' },
    { label: '上海', value: 'shanghai' }
  ]
}
```

### 5. 单选框组 (radio)

```javascript
{
  type: 'radio',
  prop: 'gender',
  label: '性别',
  options: [
    { label: '男', value: 'male' },
    { label: '女', value: 'female' }
  ]
}
```

### 6. 多选框组 (checkbox)

```javascript
{
  type: 'checkbox',
  prop: 'hobbies',
  label: '爱好',
  min: 1,
  max: 3,
  options: [
    { label: '阅读', value: 'reading' },
    { label: '运动', value: 'sports' }
  ]
}
```

### 7. 日期选择器 (date)

```javascript
{
  type: 'date',
  prop: 'birthday',
  label: '生日',
  dateType: 'date', // date, datetime, daterange 等
  format: 'YYYY-MM-DD',
  valueFormat: 'YYYY-MM-DD'
}
```

### 8. 时间选择器 (time)

```javascript
{
  type: 'time',
  prop: 'workTime',
  label: '工作时间',
  format: 'HH:mm:ss',
  valueFormat: 'HH:mm:ss'
}
```

### 9. 开关 (switch)

```javascript
{
  type: 'switch',
  prop: 'isActive',
  label: '状态',
  activeText: '启用',
  inactiveText: '禁用',
  activeValue: true,
  inactiveValue: false
}
```

### 10. 滑块 (slider)

```javascript
{
  type: 'slider',
  prop: 'score',
  label: '评分',
  min: 0,
  max: 100,
  step: 1,
  showInput: true
}
```

### 11. 评分 (rate)

```javascript
{
  type: 'rate',
  prop: 'rating',
  label: '满意度',
  max: 5,
  allowHalf: true,
  showText: true
}
```

### 12. 颜色选择器 (color)

```javascript
{
  type: 'color',
  prop: 'themeColor',
  label: '主题色',
  showAlpha: false,
  colorFormat: 'hex'
}
```

### 13. 级联选择器 (cascader)

```javascript
{
  type: 'cascader',
  prop: 'region',
  label: '地区',
  options: [
    {
      value: 'beijing',
      label: '北京',
      children: [
        { value: 'chaoyang', label: '朝阳区' }
      ]
    }
  ]
}
```

### 14. 自定义插槽 (slot)

```javascript
{
  type: 'slot',
  prop: 'custom',
  label: '自定义',
  slotName: 'custom-slot'
}
```

在模板中使用：

```vue
<DynamicForm :config="config">
  <template #custom-slot="{ item, formData }">
    <el-upload>
      <el-button>上传文件</el-button>
    </el-upload>
  </template>
</DynamicForm>
```

## 事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| update:modelValue | 表单数据更新 | formData |
| input | 输入事件 | { item, value, formData } |
| change | 值改变事件 | { item, value, formData } |
| blur | 失焦事件 | { item, event, formData } |
| focus | 聚焦事件 | { item, event, formData } |

## 方法

通过 ref 调用组件方法：

| 方法名 | 说明 | 参数 |
|--------|------|------|
| validate | 验证表单 | callback |
| validateField | 验证指定字段 | props, callback |
| resetFields | 重置表单 | - |
| clearValidate | 清除验证 | props |
| getFormData | 获取表单数据 | - |
| setFormData | 设置表单数据 | data |

## 示例文件

- `config.example.js` - 配置示例
- `usage.example.vue` - 使用示例

## 注意事项

1. 确保每个表单项的 `prop` 属性唯一
2. 验证规则遵循 Element Plus 的规则格式
3. 自定义插槽需要在配置中指定 `slotName`
4. 组件支持 Element Plus 的所有原生属性和事件
