// 动态表单组件类型定义

export type FormItemType = 
  | 'input'
  | 'textarea'
  | 'number'
  | 'select'
  | 'radio'
  | 'checkbox'
  | 'date'
  | 'time'
  | 'switch'
  | 'slider'
  | 'rate'
  | 'color'
  | 'cascader'
  | 'slot'

export interface FormOption {
  label: string
  value: any
  disabled?: boolean
  children?: FormOption[]
}

export interface FormRule {
  required?: boolean
  message?: string
  trigger?: string | string[]
  type?: string
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (rule: any, value: any, callback: (error?: Error) => void) => void
}

export interface BaseFormItem {
  type: FormItemType
  prop: string
  label: string
  placeholder?: string
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  rules?: FormRule[]
  defaultValue?: any
  formItemProps?: Record<string, any>
  props?: Record<string, any>
  
  // 事件回调
  onInput?: (value: any, formData: Record<string, any>) => void
  onChange?: (value: any, formData: Record<string, any>) => void
  onBlur?: (event: Event, formData: Record<string, any>) => void
  onFocus?: (event: Event, formData: Record<string, any>) => void
  onVisibleChange?: (visible: boolean, formData: Record<string, any>) => void
  onRemoveTag?: (tag: any, formData: Record<string, any>) => void
  onClear?: (formData: Record<string, any>) => void
  onActiveChange?: (color: string, formData: Record<string, any>) => void
  onExpandChange?: (value: any, formData: Record<string, any>) => void
}

export interface InputFormItem extends BaseFormItem {
  type: 'input'
  inputType?: 'text' | 'email' | 'url' | 'tel' | 'password'
  maxlength?: number
  minlength?: number
  showWordLimit?: boolean
  clearable?: boolean
  showPassword?: boolean
  prefixIcon?: string
  suffixIcon?: string
}

export interface TextareaFormItem extends BaseFormItem {
  type: 'textarea'
  rows?: number
  maxlength?: number
  minlength?: number
  showWordLimit?: boolean
  resize?: 'none' | 'both' | 'horizontal' | 'vertical'
}

export interface NumberFormItem extends BaseFormItem {
  type: 'number'
  min?: number
  max?: number
  step?: number
  precision?: number
  controls?: boolean
  controlsPosition?: 'right' | ''
}

export interface SelectFormItem extends BaseFormItem {
  type: 'select'
  options: FormOption[]
  multiple?: boolean
  clearable?: boolean
  filterable?: boolean
  remote?: boolean
  remoteMethod?: (query: string) => void
  loading?: boolean
  collapseTags?: boolean
  collapseTagsTooltip?: boolean
  maxCollapseTags?: number
}

export interface RadioFormItem extends BaseFormItem {
  type: 'radio'
  options: FormOption[]
  size?: 'large' | 'default' | 'small'
  border?: boolean
}

export interface CheckboxFormItem extends BaseFormItem {
  type: 'checkbox'
  options: FormOption[]
  min?: number
  max?: number
  size?: 'large' | 'default' | 'small'
  border?: boolean
}

export interface DateFormItem extends BaseFormItem {
  type: 'date'
  dateType?: 'year' | 'month' | 'date' | 'dates' | 'datetime' | 'week' | 'datetimerange' | 'daterange' | 'monthrange'
  format?: string
  valueFormat?: string
  startPlaceholder?: string
  endPlaceholder?: string
  rangeSeparator?: string
  defaultTime?: Date | [Date, Date]
  disabledDate?: (time: Date) => boolean
  shortcuts?: Array<{
    text: string
    value: Date | (() => Date)
  }>
  editable?: boolean
  size?: 'large' | 'default' | 'small'
}

export interface TimeFormItem extends BaseFormItem {
  type: 'time'
  format?: string
  valueFormat?: string
  startPlaceholder?: string
  endPlaceholder?: string
  rangeSeparator?: string
  isRange?: boolean
  arrowControl?: boolean
  disabledHours?: () => number[]
  disabledMinutes?: (hour: number) => number[]
  disabledSeconds?: (hour: number, minute: number) => number[]
  editable?: boolean
  size?: 'large' | 'default' | 'small'
}

export interface SwitchFormItem extends BaseFormItem {
  type: 'switch'
  size?: 'large' | 'default' | 'small'
  width?: number
  inlinePrompt?: boolean
  activeIcon?: string
  inactiveIcon?: string
  activeText?: string
  inactiveText?: string
  activeValue?: any
  inactiveValue?: any
  activeColor?: string
  inactiveColor?: string
  borderColor?: string
}

export interface SliderFormItem extends BaseFormItem {
  type: 'slider'
  min?: number
  max?: number
  step?: number
  showInput?: boolean
  showInputControls?: boolean
  size?: 'large' | 'default' | 'small'
  inputSize?: 'large' | 'default' | 'small'
  showStops?: boolean
  showTooltip?: boolean
  formatTooltip?: (value: number) => string
  range?: boolean
  vertical?: boolean
  height?: string
  sliderLabel?: string
  debounce?: number
  tooltipClass?: string
  marks?: Record<number, string>
}

export interface RateFormItem extends BaseFormItem {
  type: 'rate'
  max?: number
  allowHalf?: boolean
  lowThreshold?: number
  highThreshold?: number
  colors?: string[] | Record<number, string>
  voidColor?: string
  disabledVoidColor?: string
  iconClasses?: string[] | Record<number, string>
  voidIconClass?: string
  disabledVoidIconClass?: string
  showText?: boolean
  showScore?: boolean
  textColor?: string
  texts?: string[]
  scoreTemplate?: string
  size?: 'large' | 'default' | 'small'
}

export interface ColorFormItem extends BaseFormItem {
  type: 'color'
  showAlpha?: boolean
  colorFormat?: 'hsl' | 'hsv' | 'hex' | 'rgb'
  predefine?: string[]
  size?: 'large' | 'default' | 'small'
}

export interface CascaderFormItem extends BaseFormItem {
  type: 'cascader'
  options: FormOption[]
  showAllLevels?: boolean
  collapseTags?: boolean
  collapseTagsTooltip?: boolean
  separator?: string
  size?: 'large' | 'default' | 'small'
  cascaderProps?: Record<string, any>
  filterable?: boolean
  filterMethod?: (node: any, keyword: string) => boolean
}

export interface SlotFormItem extends BaseFormItem {
  type: 'slot'
  slotName: string
}

export type FormItem = 
  | InputFormItem
  | TextareaFormItem
  | NumberFormItem
  | SelectFormItem
  | RadioFormItem
  | CheckboxFormItem
  | DateFormItem
  | TimeFormItem
  | SwitchFormItem
  | SliderFormItem
  | RateFormItem
  | ColorFormItem
  | CascaderFormItem
  | SlotFormItem

export interface FormConfig {
  labelWidth?: string
  labelPosition?: 'left' | 'right' | 'top'
  size?: 'large' | 'default' | 'small'
  disabled?: boolean
  inline?: boolean
  formProps?: Record<string, any>
  items: FormItem[]
}

export interface FormEmitData {
  item: FormItem
  value?: any
  event?: Event
  formData: Record<string, any>
  visible?: boolean
  tag?: any
  color?: string
}

export interface DynamicFormProps {
  config: FormConfig
  modelValue?: Record<string, any>
}

export interface DynamicFormEmits {
  'update:modelValue': [value: Record<string, any>]
  'input': [data: FormEmitData]
  'change': [data: FormEmitData]
  'blur': [data: FormEmitData]
  'focus': [data: FormEmitData]
  'visible-change': [data: FormEmitData]
  'remove-tag': [data: FormEmitData]
  'clear': [data: FormEmitData]
  'active-change': [data: FormEmitData]
  'expand-change': [data: FormEmitData]
}

export interface DynamicFormMethods {
  validate: (callback?: (valid: boolean) => void) => Promise<boolean>
  validateField: (props: string | string[], callback?: (errorMessage: string) => void) => void
  resetFields: () => void
  scrollToField: (prop: string) => void
  clearValidate: (props?: string | string[]) => void
  getFormData: () => Record<string, any>
  setFormData: (data: Record<string, any>) => void
}
