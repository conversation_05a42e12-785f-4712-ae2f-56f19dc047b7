# 动态表单 Span 布局控制指南

## 概述

动态表单组件现在支持使用 `span` 属性来控制表单字段的布局宽度，基于 Element Plus 的 24 栅格系统。

## 基本用法

### 栅格系统说明

- 总共 24 列
- `span=24`: 占据整行（100%宽度）
- `span=12`: 占据半行（50%宽度，一行两个）
- `span=8`: 占据三分之一行（33.33%宽度，一行三个）
- `span=6`: 占据四分之一行（25%宽度，一行四个）

### 配置示例

```javascript
export const getFormConfig = () => {
  return {
    labelWidth: '100px',
    gutter: 20, // 栅格间隔
    items: [
      // 全宽字段
      {
        type: 'textarea',
        prop: 'description',
        label: '描述',
        span: 24 // 占据整行
      },
      // 半宽字段
      {
        type: 'input',
        prop: 'name',
        label: '姓名',
        span: 12 // 占据一半，一行两个
      },
      {
        type: 'input',
        prop: 'email',
        label: '邮箱',
        span: 12 // 占据一半，一行两个
      },
      // 三分之一宽字段
      {
        type: 'select',
        prop: 'gender',
        label: '性别',
        span: 8, // 占据1/3，一行三个
        options: [...]
      },
      {
        type: 'select',
        prop: 'status',
        label: '状态',
        span: 8, // 占据1/3，一行三个
        options: [...]
      },
      {
        type: 'date',
        prop: 'birthday',
        label: '生日',
        span: 8 // 占据1/3，一行三个
      }
    ]
  }
}
```

## 布局模式

### 1. 表单布局（推荐）
- 使用 `el-row` 和 `el-col` 进行栅格布局
- 支持响应式设计
- 更灵活的布局控制

### 2. 内联布局（搜索表单）
- 保持 `inline: true` 用于搜索表单
- 同时支持 `span` 属性进行宽度控制
- 适合紧凑的搜索界面

## 常用布局组合

### 用户信息表单
```javascript
items: [
  { prop: 'username', span: 12 },    // 用户名（半宽）
  { prop: 'email', span: 12 },       // 邮箱（半宽）
  { prop: 'age', span: 8 },          // 年龄（1/3宽）
  { prop: 'gender', span: 8 },       // 性别（1/3宽）
  { prop: 'status', span: 8 },       // 状态（1/3宽）
  { prop: 'description', span: 24 }  // 描述（全宽）
]
```

### 产品信息表单
```javascript
items: [
  { prop: 'name', span: 12 },        // 产品名称（半宽）
  { prop: 'price', span: 8 },        // 价格（1/3宽）
  { prop: 'category', span: 8 },     // 分类（1/3宽）
  { prop: 'brand', span: 8 },        // 品牌（1/3宽）
  { prop: 'description', span: 24 }  // 描述（全宽）
]
```

### 搜索表单
```javascript
items: [
  { prop: 'keyword', span: 6 },      // 关键词（1/4宽）
  { prop: 'category', span: 6 },     // 分类（1/4宽）
  { prop: 'status', span: 6 },       // 状态（1/4宽）
  { prop: 'dateRange', span: 6 }     // 日期范围（1/4宽）
]
```

## 注意事项

1. **默认值**: 如果不设置 `span`，默认为 `24`（全宽）
2. **栅格间隔**: 通过 `gutter` 属性设置列间距
3. **响应式**: 可以根据需要扩展支持响应式断点
4. **兼容性**: 与原有的 `inline` 属性兼容，搜索表单可以继续使用内联模式

## 测试页面

访问 `/form-test` 页面查看各种布局示例：
- 用户表单演示（混合 span 布局）
- 产品表单演示（不同宽度组合）
- 布局演示表单（所有 span 组合展示）
- 搜索表单演示（内联 + span 控制）
