// 动态表单配置文件

/**
 * 获取用户表单配置
 * @param {Object} options - 动态选项数据
 * @returns {Object} 表单配置
 */
export const getUserFormConfig = (options = {}) => {
  return {
    labelWidth: '100px',
    gutter: 20, // 栅格间隔
    items: [
      {
        type: 'input',
        prop: 'username',
        label: '用户名',
        placeholder: '请输入用户名',
        required: true,
        clearable: true,
        span: 12, // 占据12列，即一行两个
        rules: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ]
      },
      {
        type: 'input',
        prop: 'email',
        label: '邮箱',
        placeholder: '请输入邮箱',
        inputType: 'email',
        clearable: true,
        span: 12, // 占据12列，即一行两个
        rules: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      },
      {
        type: 'number',
        prop: 'age',
        label: '年龄',
        min: 1,
        max: 120,
        placeholder: '请输入年龄',
        span: 8 // 占据8列，即一行三个
      },
      {
        type: 'select',
        prop: 'gender',
        label: '性别',
        placeholder: '请选择性别',
        span: 8, // 占据8列，即一行三个
        options: options.genderOptions || [
          { label: '男', value: 'male' },
          { label: '女', value: 'female' },
          { label: '其他', value: 'other' }
        ]
      },
      {
        type: 'radio',
        prop: 'status',
        label: '状态',
        span: 8, // 占据8列，即一行三个
        options: options.statusOptions || [
          { label: '活跃', value: 'active' },
          { label: '非活跃', value: 'inactive' }
        ],
        defaultValue: 'active'
      },
      {
        type: 'checkbox',
        prop: 'interests',
        label: '兴趣',
        span: 8, // 占据8列，即一行三个
        options: options.interestOptions || [
          { label: '阅读', value: 'reading' },
          { label: '运动', value: 'sports' },
          { label: '音乐', value: 'music' },
          { label: '旅行', value: 'travel' }
        ]
      },
      {
        type: 'select',
        prop: 'department',
        label: '部门',
        placeholder: '请选择部门',
        clearable: true,
        filterable: true,
        span: 12, // 占据12列，即一行两个
        options: options.departmentOptions || []
      },
      {
        type: 'cascader',
        prop: 'region',
        label: '地区',
        placeholder: '请选择地区',
        clearable: true,
        span: 12, // 占据12列，即一行两个
        options: options.regionOptions || []
      },
      {
        type: 'date',
        prop: 'birthday',
        label: '生日',
        dateType: 'date',
        placeholder: '请选择生日',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        span: 12 // 占据12列，即一行两个
      },
      {
        type: 'switch',
        prop: 'isVip',
        label: 'VIP用户',
        activeText: '是',
        inactiveText: '否',
        span: 12 // 占据12列，即一行两个
      }
    ]
  }
}

/**
 * 获取产品表单配置
 * @param {Object} options - 动态选项数据
 * @returns {Object} 表单配置
 */
export const getProductFormConfig = (options = {}) => {
  return {
    labelWidth: '120px',
    gutter: 20, // 栅格间隔
    items: [
      {
        type: 'input',
        prop: 'name',
        label: '产品名称',
        placeholder: '请输入产品名称',
        required: true,
        span: 12, // 占据12列，即一行两个
        rules: [{ required: true, message: '请输入产品名称', trigger: 'blur' }]
      },
      {
        type: 'textarea',
        prop: 'description',
        label: '产品描述',
        placeholder: '请输入产品描述',
        rows: 3,
        maxlength: 500,
        span: 24 // 占据24列，即独占一行
      },
      {
        type: 'number',
        prop: 'price',
        label: '价格',
        min: 0,
        placeholder: '请输入价格',
        span: 8 // 占据8列，即一行三个
      },
      {
        type: 'select',
        prop: 'category',
        label: '分类',
        placeholder: '请选择分类',
        clearable: true,
        filterable: true,
        span: 8, // 占据8列，即一行三个
        options: options.categoryOptions || [
          { label: '电子产品', value: 'electronics' },
          { label: '服装', value: 'clothing' },
          { label: '食品', value: 'food' },
          { label: '图书', value: 'books' }
        ]
      },
      {
        type: 'select',
        prop: 'brand',
        label: '品牌',
        placeholder: '请选择品牌',
        clearable: true,
        filterable: true,
        span: 8, // 占据8列，即一行三个
        options: options.brandOptions || []
      },
      {
        type: 'select',
        prop: 'tags',
        label: '标签',
        placeholder: '请选择标签',
        multiple: true,
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        maxCollapseTags: 2,
        options: options.tagOptions || []
      },
      {
        type: 'cascader',
        prop: 'salesRegion',
        label: '销售区域',
        placeholder: '请选择销售区域',
        clearable: true,
        showAllLevels: true,
        options: options.regionOptions || [
          {
            value: 'china',
            label: '中国',
            children: [
              {
                value: 'beijing',
                label: '北京',
                children: [
                  { value: 'chaoyang', label: '朝阳区' },
                  { value: 'haidian', label: '海淀区' }
                ]
              },
              {
                value: 'shanghai',
                label: '上海',
                children: [
                  { value: 'huangpu', label: '黄浦区' },
                  { value: 'xuhui', label: '徐汇区' }
                ]
              }
            ]
          }
        ]
      },
      {
        type: 'radio',
        prop: 'status',
        label: '状态',
        options: options.productStatusOptions || [
          { label: '上架', value: 'online' },
          { label: '下架', value: 'offline' },
          { label: '草稿', value: 'draft' }
        ],
        defaultValue: 'draft'
      },
      {
        type: 'slider',
        prop: 'quality',
        label: '质量评分',
        min: 0,
        max: 100,
        showInput: true,
        defaultValue: 80
      },
      {
        type: 'rate',
        prop: 'rating',
        label: '用户评分',
        max: 5,
        allowHalf: true,
        showText: true,
        texts: ['极差', '失望', '一般', '满意', '惊喜']
      },
      {
        type: 'color',
        prop: 'themeColor',
        label: '主题色',
        showAlpha: false,
        predefine: ['#ff4500', '#ff8c00', '#ffd700', '#90ee90', '#00ced1', '#1e90ff', '#c71585']
      },
      {
        type: 'slot',
        prop: 'image',
        label: '产品图片',
        slotName: 'image-upload'
      }
    ]
  }
}

/**
 * 获取订单表单配置
 * @param {Object} options - 动态选项数据
 * @returns {Object} 表单配置
 */
export const getOrderFormConfig = (options = {}) => {
  return {
    labelWidth: '120px',
    items: [
      {
        type: 'input',
        prop: 'orderNo',
        label: '订单号',
        placeholder: '请输入订单号',
        required: true,
        clearable: true,
        rules: [{ required: true, message: '请输入订单号', trigger: 'blur' }]
      },
      {
        type: 'select',
        prop: 'customer',
        label: '客户',
        placeholder: '请选择客户',
        clearable: true,
        filterable: true,
        remote: true,
        remoteMethod: options.searchCustomer,
        loading: options.customerLoading || false,
        options: options.customerOptions || []
      },
      {
        type: 'select',
        prop: 'products',
        label: '产品',
        placeholder: '请选择产品',
        multiple: true,
        clearable: true,
        filterable: true,
        collapseTags: true,
        options: options.productOptions || []
      },
      {
        type: 'date',
        prop: 'orderDate',
        label: '订单日期',
        dateType: 'datetime',
        placeholder: '请选择订单日期',
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        defaultValue: new Date()
      },
      {
        type: 'date',
        prop: 'deliveryDate',
        label: '交付日期',
        dateType: 'daterange',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        rangeSeparator: '至',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD'
      },
      {
        type: 'number',
        prop: 'totalAmount',
        label: '总金额',
        min: 0,
        precision: 2,
        placeholder: '请输入总金额',
        required: true
      },
      {
        type: 'select',
        prop: 'paymentMethod',
        label: '支付方式',
        placeholder: '请选择支付方式',
        options: options.paymentOptions || [
          { label: '现金', value: 'cash' },
          { label: '银行卡', value: 'card' },
          { label: '支付宝', value: 'alipay' },
          { label: '微信', value: 'wechat' }
        ]
      },
      {
        type: 'radio',
        prop: 'priority',
        label: '优先级',
        options: options.priorityOptions || [
          { label: '低', value: 'low' },
          { label: '中', value: 'medium' },
          { label: '高', value: 'high' },
          { label: '紧急', value: 'urgent' }
        ],
        defaultValue: 'medium'
      },
      {
        type: 'switch',
        prop: 'isUrgent',
        label: '加急处理',
        activeText: '是',
        inactiveText: '否'
      },
      {
        type: 'textarea',
        prop: 'remarks',
        label: '备注',
        placeholder: '请输入备注信息',
        rows: 3,
        maxlength: 200,
        showWordLimit: true
      }
    ]
  }
}

/**
 * 获取简单搜索表单配置
 * @param {Object} options - 动态选项数据
 * @returns {Object} 表单配置
 */
export const getSearchFormConfig = (options = {}) => {
  return {
    labelWidth: '80px',
    inline: true, // 保持内联布局用于搜索表单
    gutter: 16, // 栅格间隔
    items: [
      {
        type: 'input',
        prop: 'keyword',
        label: '关键词',
        placeholder: '请输入关键词',
        clearable: true,
        span: 6, // 占据6列，即一行四个
        props: { style: { width: '200px' } }
      },
      {
        type: 'select',
        prop: 'category',
        label: '分类',
        placeholder: '请选择分类',
        clearable: true,
        span: 6, // 占据6列，即一行四个
        options: options.categoryOptions || [],
        props: { style: { width: '150px' } }
      },
      {
        type: 'select',
        prop: 'status',
        label: '状态',
        placeholder: '请选择状态',
        clearable: true,
        span: 6, // 占据6列，即一行四个
        options: options.statusOptions || [],
        props: { style: { width: '120px' } }
      },
      {
        type: 'date',
        prop: 'dateRange',
        label: '日期',
        dateType: 'daterange',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        span: 6, // 占据6列，即一行四个
        props: { style: { width: '240px' } }
      }
    ]
  }
}

// 默认选项数据
export const defaultOptions = {
  // 性别选项
  genderOptions: [
    { label: '男', value: 'male' },
    { label: '女', value: 'female' },
    { label: '其他', value: 'other' }
  ],

  // 状态选项
  statusOptions: [
    { label: '启用', value: 'active' },
    { label: '禁用', value: 'inactive' },
    { label: '待审核', value: 'pending' }
  ],

  // 兴趣选项
  interestOptions: [
    { label: '阅读', value: 'reading' },
    { label: '运动', value: 'sports' },
    { label: '音乐', value: 'music' },
    { label: '旅行', value: 'travel' },
    { label: '摄影', value: 'photography' },
    { label: '编程', value: 'programming' }
  ],

  // 部门选项
  departmentOptions: [
    { label: '技术部', value: 'tech' },
    { label: '产品部', value: 'product' },
    { label: '运营部', value: 'operation' },
    { label: '市场部', value: 'marketing' },
    { label: '人事部', value: 'hr' },
    { label: '财务部', value: 'finance' }
  ],

  // 地区选项
  regionOptions: [
    {
      value: 'china',
      label: '中国',
      children: [
        {
          value: 'beijing',
          label: '北京市',
          children: [
            { value: 'chaoyang', label: '朝阳区' },
            { value: 'haidian', label: '海淀区' },
            { value: 'dongcheng', label: '东城区' },
            { value: 'xicheng', label: '西城区' }
          ]
        },
        {
          value: 'shanghai',
          label: '上海市',
          children: [
            { value: 'huangpu', label: '黄浦区' },
            { value: 'xuhui', label: '徐汇区' },
            { value: 'changning', label: '长宁区' },
            { value: 'jingan', label: '静安区' }
          ]
        },
        {
          value: 'guangzhou',
          label: '广州市',
          children: [
            { value: 'tianhe', label: '天河区' },
            { value: 'yuexiu', label: '越秀区' },
            { value: 'haizhu', label: '海珠区' }
          ]
        }
      ]
    }
  ],

  // 产品分类选项
  categoryOptions: [
    { label: '电子产品', value: 'electronics' },
    { label: '服装鞋帽', value: 'clothing' },
    { label: '食品饮料', value: 'food' },
    { label: '图书文具', value: 'books' },
    { label: '家居用品', value: 'home' },
    { label: '运动户外', value: 'sports' }
  ],

  // 品牌选项
  brandOptions: [
    { label: '苹果', value: 'apple' },
    { label: '华为', value: 'huawei' },
    { label: '小米', value: 'xiaomi' },
    { label: '三星', value: 'samsung' },
    { label: '联想', value: 'lenovo' }
  ],

  // 标签选项
  tagOptions: [
    { label: '热销', value: 'hot' },
    { label: '新品', value: 'new' },
    { label: '促销', value: 'sale' },
    { label: '推荐', value: 'recommend' },
    { label: '限量', value: 'limited' }
  ],

  // 产品状态选项
  productStatusOptions: [
    { label: '上架', value: 'online' },
    { label: '下架', value: 'offline' },
    { label: '草稿', value: 'draft' },
    { label: '审核中', value: 'reviewing' }
  ],

  // 支付方式选项
  paymentOptions: [
    { label: '现金', value: 'cash' },
    { label: '银行卡', value: 'card' },
    { label: '支付宝', value: 'alipay' },
    { label: '微信支付', value: 'wechat' },
    { label: '信用卡', value: 'credit' }
  ],

  // 优先级选项
  priorityOptions: [
    { label: '低', value: 'low' },
    { label: '中', value: 'medium' },
    { label: '高', value: 'high' },
    { label: '紧急', value: 'urgent' }
  ]
}
