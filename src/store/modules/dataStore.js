import { defineStore } from 'pinia'
import { getDataList, getDataTreeList } from '../../api/dataStore'
import { ref, watch } from 'vue'
import { computed } from 'vue'

export const useDataStore = defineStore('dataStore', () => {
  const firstTypes = ref([]) // 一级类型列表
  const secondTypes = ref([]) // 二级类型列表
  const thirdTypes = ref([]) // 三级类型列表

  const tableAttr = ref([])
  const leftCheckList = ref([])
  const rightCheckList = ref([])

  const attribute = ref([])

  const mapData = ref([])

  const currentSecondTypes = computed(() => {
    return secondTypes.value.filter((item) => item.attributePid === currentId.value)
  })

  const currentThirdTypes = computed(() => {
    if (currentSecondTypes.value.length == 0) {
      return thirdTypes.value.filter((item) => item.attributePid === currentId.value)
    } else {
      return thirdTypes.value.filter((item) => item.attributePid === currentSecondId.value)
    }
  })

  const currentTableConfig = computed(() => {
    const tableConfig = constructTableConfig()
    tableConfig.push({
      prop: 'handler',
      label: '操作',
      minWidth: '220',
      slotName: 'handler',
      fixed: 'right',
      align: 'center'
    })
    return tableConfig
  })

  // 当前选中的id
  const currentId = ref('')

  // 当前选中的二级id
  const currentSecondId = ref('')

  async function getDataListAction() {
    firstTypes.value = []
    secondTypes.value = []
    thirdTypes.value = []

    const res = await getDataTreeList()
    res.data.forEach((item) => {
      switch (item.level) {
        case 1:
          firstTypes.value.push(item)
          break
        case 2:
          secondTypes.value.push(item)
          break
        case 3:
          thirdTypes.value.push(item)
          break
      }
    })

    currentId.value = firstTypes.value[0].attributeId
  }

  // 构建table config数据
  function constructTableConfig() {
    return currentThirdTypes.value.map((item) => {
      return {
        prop: item.attributeLabel,
        label: item.attributeName,
        minWidth: '100',
        slotName: item.attributeLabel,
        align: 'center'
      }
    })
  }

  // 修改数据绑定关系
  function useValueFindDataAction(value, flag, type) {
    if (type === 'attr') {
      attribute.value.forEach((item) => {
        if (item.attributeLabel === value) {
          item.isDisabled = flag
        }
      })
    } else {
      tableAttr.value.forEach((item) => {
        if (item.attributeLabel === value) {
          item.isDisabled = flag
        }
      })
    }
  }

  watch(
    currentSecondTypes,
    (newValue) => {
      if (newValue.length > 0) currentSecondId.value = newValue[0].attributeId
    },
    {
      deep: true,
      immediate: true
    }
  )

  return {
    firstTypes,
    secondTypes,
    thirdTypes,
    currentSecondTypes,
    currentThirdTypes,
    currentId,
    currentSecondId,
    currentTableConfig,
    tableAttr,
    leftCheckList,
    rightCheckList,
    attribute,
    mapData,
    getDataListAction,
    useValueFindDataAction
  }
})
