# Vue3 动态表单组件

基于 Vue3 和 Element Plus 的动态表单组件，支持通过配置文件动态生成表单，支持动态选项传入。

## 特性

- ✅ 基于 Vue3 Composition API
- ✅ 使用 Element Plus UI 组件库
- ✅ 配置驱动的表单生成
- ✅ 支持 14 种表单字段类型
- ✅ 动态选项数据支持
- ✅ 完整的表单验证
- ✅ 事件处理机制
- ✅ 自定义插槽支持
- ✅ TypeScript 类型定义（可选）

## 快速开始

### 1. 基础用法

```vue
<template>
  <DynamicForm
    v-model="formData"
    :config="formConfig"
    @change="handleFormChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import DynamicForm from '@/components/Form/index.vue'
import { getUserFormConfig, defaultOptions } from '@/config/form-configs.js'

const formData = ref({})
const formConfig = getUserFormConfig(defaultOptions)

const handleFormChange = (data) => {
  console.log('表单数据变化:', data)
}
</script>
```

### 2. 动态选项用法

```vue
<template>
  <div>
    <el-button @click="loadDepartmentOptions">加载部门选项</el-button>
    
    <DynamicForm
      v-model="formData"
      :config="formConfig"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import DynamicForm from '@/components/Form/index.vue'
import { getUserFormConfig, defaultOptions } from '@/config/form-configs.js'

const formData = ref({})

// 动态选项数据
const dynamicOptions = reactive({
  departmentOptions: []
})

// 表单配置 - 使用计算属性实现动态更新
const formConfig = computed(() => {
  return getUserFormConfig({
    ...defaultOptions,
    ...dynamicOptions
  })
})

// 加载部门选项
const loadDepartmentOptions = () => {
  // 模拟API调用
  setTimeout(() => {
    dynamicOptions.departmentOptions = [
      { label: '技术部', value: 'tech' },
      { label: '产品部', value: 'product' },
      { label: '运营部', value: 'operation' }
    ]
  }, 300)
}
</script>
```

## 配置文件结构

### 表单配置对象

```javascript
{
  labelWidth: '100px',    // 标签宽度
  inline: false,          // 是否行内表单
  items: [                // 表单项配置数组
    {
      type: 'input',      // 字段类型
      prop: 'username',   // 字段属性名
      label: '用户名',     // 字段标签
      placeholder: '请输入用户名',
      required: true,     // 是否必填
      rules: [],          // 验证规则
      // ... 其他Element Plus组件属性
    }
  ]
}
```

### 支持的字段类型

| 类型 | 说明 | Element Plus 组件 |
|------|------|------------------|
| `input` | 文本输入框 | `el-input` |
| `textarea` | 多行文本框 | `el-input` |
| `number` | 数字输入框 | `el-input-number` |
| `select` | 下拉选择框 | `el-select` |
| `radio` | 单选按钮组 | `el-radio-group` |
| `checkbox` | 多选框组 | `el-checkbox-group` |
| `date` | 日期选择器 | `el-date-picker` |
| `time` | 时间选择器 | `el-time-picker` |
| `switch` | 开关 | `el-switch` |
| `slider` | 滑块 | `el-slider` |
| `rate` | 评分 | `el-rate` |
| `color` | 颜色选择器 | `el-color-picker` |
| `cascader` | 级联选择器 | `el-cascader` |
| `slot` | 自定义插槽 | 自定义内容 |

## 预定义配置函数

### getUserFormConfig(options)

生成用户表单配置

```javascript
import { getUserFormConfig, defaultOptions } from '@/config/form-configs.js'

const config = getUserFormConfig({
  ...defaultOptions,
  departmentOptions: [
    { label: '技术部', value: 'tech' },
    { label: '产品部', value: 'product' }
  ]
})
```

### getProductFormConfig(options)

生成产品表单配置

```javascript
import { getProductFormConfig, defaultOptions } from '@/config/form-configs.js'

const config = getProductFormConfig({
  ...defaultOptions,
  brandOptions: [
    { label: '苹果', value: 'apple' },
    { label: '华为', value: 'huawei' }
  ]
})
```

### getOrderFormConfig(options)

生成订单表单配置

```javascript
import { getOrderFormConfig, defaultOptions } from '@/config/form-configs.js'

const config = getOrderFormConfig({
  ...defaultOptions,
  customerOptions: [
    { label: '客户A', value: 'customer_a' },
    { label: '客户B', value: 'customer_b' }
  ]
})
```

### getSearchFormConfig(options)

生成搜索表单配置

```javascript
import { getSearchFormConfig, defaultOptions } from '@/config/form-configs.js'

const config = getSearchFormConfig({
  ...defaultOptions,
  statusOptions: [
    { label: '启用', value: 'active' },
    { label: '禁用', value: 'inactive' }
  ]
})
```

## 动态选项数据

### 支持的动态选项

- `genderOptions` - 性别选项
- `statusOptions` - 状态选项
- `interestOptions` - 兴趣选项
- `departmentOptions` - 部门选项
- `regionOptions` - 地区选项
- `categoryOptions` - 分类选项
- `brandOptions` - 品牌选项
- `tagOptions` - 标签选项
- `productStatusOptions` - 产品状态选项
- `paymentOptions` - 支付方式选项
- `priorityOptions` - 优先级选项

### 动态加载示例

```javascript
// 从API加载选项数据
const loadOptions = async () => {
  try {
    const response = await fetch('/api/departments')
    const departments = await response.json()
    
    dynamicOptions.departmentOptions = departments.map(dept => ({
      label: dept.name,
      value: dept.id
    }))
  } catch (error) {
    console.error('加载部门数据失败:', error)
  }
}
```

## 表单验证

### 内置验证规则

```javascript
{
  type: 'input',
  prop: 'email',
  label: '邮箱',
  rules: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}
```

### 自定义验证器

```javascript
{
  type: 'input',
  prop: 'password',
  label: '密码',
  rules: [
    {
      validator: (rule, value, callback) => {
        if (value.length < 6) {
          callback(new Error('密码长度不能少于6位'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}
```

## 事件处理

### 表单级事件

```vue
<DynamicForm
  v-model="formData"
  :config="formConfig"
  @change="handleChange"
  @input="handleInput"
  @focus="handleFocus"
  @blur="handleBlur"
/>
```

### 字段级事件

```javascript
{
  type: 'select',
  prop: 'province',
  label: '省份',
  options: provinceOptions,
  onChange: (value, formData) => {
    // 省份变化时清空城市
    formData.city = ''
    loadCities(value)
  }
}
```

## 自定义插槽

```vue
<DynamicForm v-model="formData" :config="formConfig">
  <template #image-upload="{ item, formData }">
    <el-upload
      action="/api/upload"
      :show-file-list="false"
      :on-success="handleUploadSuccess"
    >
      <img v-if="formData[item.prop]" :src="formData[item.prop]" class="image" />
      <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
    </el-upload>
  </template>
</DynamicForm>
```

## 表单方法

```vue
<template>
  <DynamicForm ref="formRef" v-model="formData" :config="formConfig" />
  <el-button @click="validateForm">验证表单</el-button>
  <el-button @click="resetForm">重置表单</el-button>
</template>

<script setup>
import { ref } from 'vue'

const formRef = ref()

const validateForm = async () => {
  try {
    const valid = await formRef.value.validate()
    if (valid) {
      console.log('表单验证通过')
    }
  } catch (error) {
    console.log('表单验证失败:', error)
  }
}

const resetForm = () => {
  formRef.value.resetFields()
}
</script>
```

## 完整示例

查看 `src/views/form-test.vue` 文件获取完整的使用示例，包括：

- 多种表单类型切换
- 动态选项加载
- 表单验证
- 事件处理
- 自定义插槽

## 文件结构

```
src/
├── components/Form/
│   └── index.vue              # 动态表单组件
├── config/
│   └── form-configs.js        # 表单配置文件
├── examples/
│   └── dynamic-form-usage.js  # 使用示例
└── views/
    └── form-test.vue          # 测试页面
```
